"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CODE_TO_HTTP_STATUS = exports.ErrorType = exports.BUSINESS_ERROR_CODES = exports.SUCCESS_CODES = exports.NOT_FOUND_CODE = exports.FORBIDDEN_CODE = exports.UNAUTHORIZED_CODE = exports.BAD_REQUEST_CODE = exports.ERROR_CODE = exports.SUCCESS_CODE = void 0;
exports.getHttpStatusCode = getHttpStatusCode;
exports.getErrorType = getErrorType;
exports.SUCCESS_CODE = 200;
exports.ERROR_CODE = 500;
exports.BAD_REQUEST_CODE = 400;
exports.UNAUTHORIZED_CODE = 401;
exports.FORBIDDEN_CODE = 403;
exports.NOT_FOUND_CODE = 404;
exports.SUCCESS_CODES = {
    OK: 200,
    CREATED: 201,
    ACCEPTED: 202,
    PARTIAL_SUCCESS: 206,
};
exports.BUSINESS_ERROR_CODES = {
    USER_NOT_FOUND: 1001,
    USER_ALREADY_EXISTS: 1002,
    USER_DISABLED: 1003,
    USER_LOCKED: 1004,
    TOKEN_EXPIRED: 2001,
    TOKEN_INVALID: 2002,
    LOGIN_FAILED: 2003,
    PASSWORD_INCORRECT: 2004,
    ACCOUNT_LOCKED: 2005,
    PERMISSION_DENIED: 3001,
    ROLE_NOT_FOUND: 3002,
    INSUFFICIENT_PRIVILEGES: 3003,
    INSUFFICIENT_BALANCE: 4001,
    OPERATION_NOT_ALLOWED: 4002,
    RESOURCE_LOCKED: 4003,
    DUPLICATE_OPERATION: 4004,
    RESOURCE_EXPIRED: 4005,
    SYSTEM_MAINTENANCE: 5001,
    RATE_LIMIT_EXCEEDED: 5002,
    EXTERNAL_SERVICE_ERROR: 5003,
    DATABASE_ERROR: 5004,
    CACHE_ERROR: 5005,
};
var ErrorType;
(function (ErrorType) {
    ErrorType["SYSTEM_ERROR"] = "SystemError";
    ErrorType["BUSINESS_ERROR"] = "BusinessError";
    ErrorType["VALIDATION_ERROR"] = "ValidationError";
    ErrorType["AUTHENTICATION_ERROR"] = "AuthenticationError";
    ErrorType["AUTHORIZATION_ERROR"] = "AuthorizationError";
    ErrorType["NETWORK_ERROR"] = "NetworkError";
    ErrorType["DATABASE_ERROR"] = "DatabaseError";
    ErrorType["EXTERNAL_SERVICE_ERROR"] = "ExternalServiceError";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
exports.CODE_TO_HTTP_STATUS = {
    200: 200, 201: 201, 202: 202, 206: 206,
    400: 400, 401: 401, 403: 403, 404: 404, 405: 405, 409: 409, 422: 422, 429: 429,
    500: 500, 501: 501, 502: 502, 503: 503, 504: 504,
    1001: 404, 1002: 409, 1003: 403, 1004: 423,
    2001: 401, 2002: 401, 2003: 401, 2004: 401, 2005: 423,
    3001: 403, 3002: 403, 3003: 403,
    4001: 400, 4002: 400, 4003: 423, 4004: 409, 4005: 410,
    5001: 503, 5002: 429, 5003: 502, 5004: 500, 5005: 500,
};
function getHttpStatusCode(businessCode) {
    return exports.CODE_TO_HTTP_STATUS[businessCode] || 500;
}
function getErrorType(code) {
    if (code >= 1000 && code < 2000)
        return ErrorType.BUSINESS_ERROR;
    if (code >= 2000 && code < 3000)
        return ErrorType.AUTHENTICATION_ERROR;
    if (code >= 3000 && code < 4000)
        return ErrorType.AUTHORIZATION_ERROR;
    if (code >= 4000 && code < 5000)
        return ErrorType.BUSINESS_ERROR;
    if (code >= 5000 && code < 6000)
        return ErrorType.SYSTEM_ERROR;
    if (code === 400 || code === 422)
        return ErrorType.VALIDATION_ERROR;
    if (code === 401)
        return ErrorType.AUTHENTICATION_ERROR;
    if (code === 403)
        return ErrorType.AUTHORIZATION_ERROR;
    if (code >= 500)
        return ErrorType.SYSTEM_ERROR;
    return ErrorType.SYSTEM_ERROR;
}
//# sourceMappingURL=http-response.interface.js.map
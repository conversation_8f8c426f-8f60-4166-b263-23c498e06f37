# DDD架构错误处理修复指南

## 🎯 问题描述

IP地理位置模块遵循DDD架构，但之前的错误处理不符合DDD设计原则：

### ❌ **修复前的问题**
```typescript
// 控制器中抛出普通Error（不符合DDD架构）
if (!result.success) {
  throw new Error((result as any).error || '获取当前IP位置失败');
}
```

**错误响应格式**：
```json
{
  "statusCode": 500,
  "timestamp": "2025-08-02T04:24:39.928Z",
  "path": "/api/v1/ip-location/current",
  "message": "获取当前IP位置失败",
  "details": {...}
}
```

## ✅ **DDD架构的正确做法**

### **1. 抛出领域业务异常**
```typescript
// 符合DDD架构的错误处理
if (!result.success) {
  throw new UnifiedBusinessException(
    BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR,
    (result as any).error || 'IP地理位置查询失败',
    { ip: query.ip, includeRisk: query.includeRisk },
    { service: 'ip2region', operation: 'queryLocation' }
  );
}
```

### **2. 统一的错误响应格式**
```json
{
  "code": 5003,
  "msg": "IP地理位置查询失败",
  "data": { "ip": "*******", "includeRisk": false },
  "success": false,
  "timestamp": "2025-08-02T04:24:39.928Z",
  "trace": {
    "requestId": "ip-loc-1722571479928-abc123",
    "traceId": "trace-1722571479928-xyz789",
    "path": "/api/v1/ip-location/current"
  },
  "error": {
    "type": "SystemError",
    "details": {
      "service": "ip2region",
      "operation": "queryLocation"
    }
  }
}
```

## 🔧 **修复内容**

### **1. 导入DDD架构异常类**
```typescript
// DDD架构：导入领域异常
import { UnifiedBusinessException } from '../../../common/exceptions/unified-business.exception';
import { BUSINESS_ERROR_CODES } from '../../../web/http_response_result/http-response.interface';
```

### **2. 修复所有控制器方法**

#### **IP查询方法**
```typescript
if (!result.success) {
  throw new UnifiedBusinessException(
    BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR,
    (result as any).error || 'IP地理位置查询失败',
    { ip: query.ip, includeRisk: query.includeRisk },
    { service: 'ip2region', operation: 'queryLocation' }
  );
}
```

#### **风险评估方法**
```typescript
if (!result.success) {
  throw new UnifiedBusinessException(
    BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR,
    (result as any).error || '登录风险评估失败',
    { userId: request.userId, ipAddress: request.ipAddress },
    { service: 'riskAssessment', operation: 'checkLoginRisk' }
  );
}
```

#### **位置统计方法**
```typescript
if (!result.success) {
  throw new UnifiedBusinessException(
    BUSINESS_ERROR_CODES.DATABASE_ERROR,
    (result as any).error || '用户位置统计获取失败',
    { userId, days },
    { service: 'locationStats', operation: 'getUserLocationStats' }
  );
}
```

#### **可信位置设置方法**
```typescript
if (!result.success) {
  throw new UnifiedBusinessException(
    BUSINESS_ERROR_CODES.OPERATION_NOT_ALLOWED,
    (result as any).error || '设置可信位置失败',
    { userId, province: request.province, city: request.city },
    { service: 'trustedLocation', operation: 'setTrustedLocation' }
  );
}
```

#### **当前IP位置方法**
```typescript
if (!result.success) {
  throw new UnifiedBusinessException(
    BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR,
    (result as any).error || '获取当前IP位置失败',
    { clientIp },
    { service: 'currentIpLocation', operation: 'getCurrentIpLocation' }
  );
}
```

### **3. 配置统一异常过滤器**
```typescript
// main.ts 中的配置
import { UnifiedExceptionFilter } from './web/http_exception_filter/unified-exception.filter';
import { HttpResponseResultService } from './web/http_response_result/http_response_result.service';

// 获取服务实例
const loggerService = app.get(LoggerService);
const httpResponseService = app.get(HttpResponseResultService);

// DDD架构：设置统一异常过滤器
app.useGlobalFilters(new UnifiedExceptionFilter(httpResponseService, loggerService));
```

## 🎯 **DDD架构的优势**

### **1. 领域驱动的错误分类**
- **EXTERNAL_SERVICE_ERROR (5003)**: 外部服务异常（IP解析服务）
- **DATABASE_ERROR (5004)**: 数据库异常（位置统计）
- **OPERATION_NOT_ALLOWED (4002)**: 业务操作不允许（可信位置设置）

### **2. 丰富的业务上下文**
```typescript
{
  service: 'ip2region',           // 具体的服务标识
  operation: 'queryLocation',     // 具体的操作标识
  ip: '*******',                 // 业务参数
  includeRisk: false             // 业务配置
}
```

### **3. 完整的追踪信息**
```typescript
{
  requestId: 'ip-loc-1722571479928-abc123',  // 业务请求ID
  traceId: 'trace-1722571479928-xyz789',     // 全链路追踪ID
  path: '/api/v1/ip-location/current'        // 请求路径
}
```

## 🚀 **测试验证**

### **1. 测试当前IP位置接口**
```bash
curl -X GET "http://localhost:8003/api/v1/ip-location/current"
```

**期望响应**（成功）：
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "country": "中国",
    "province": "北京",
    "city": "北京",
    "isp": "电信"
  },
  "success": true,
  "timestamp": "2025-08-02T04:30:00.000Z",
  "trace": {
    "requestId": "ip-loc-1722571800000-abc123",
    "path": "/api/v1/ip-location/current"
  }
}
```

**期望响应**（失败）：
```json
{
  "code": 5003,
  "msg": "获取当前IP位置失败",
  "data": { "clientIp": "127.0.0.1" },
  "success": false,
  "timestamp": "2025-08-02T04:30:00.000Z",
  "trace": {
    "requestId": "ip-loc-1722571800000-abc123",
    "traceId": "trace-1722571800000-xyz789",
    "path": "/api/v1/ip-location/current"
  },
  "error": {
    "type": "SystemError",
    "details": {
      "service": "currentIpLocation",
      "operation": "getCurrentIpLocation"
    }
  }
}
```

### **2. 测试IP查询接口**
```bash
curl -X GET "http://localhost:8003/api/v1/ip-location/query?ip=*******&includeRisk=false"
```

### **3. 测试风险评估接口**
```bash
curl -X POST "http://localhost:8003/api/v1/ip-location/risk/check" \
  -H "Content-Type: application/json" \
  -d '{"userId": 123, "ipAddress": "*******", "userAgent": "test", "sessionId": "test-session"}'
```

## 📊 **错误码映射**

| 业务场景 | 业务错误码 | HTTP状态码 | 错误类型 |
|---------|-----------|-----------|---------|
| IP解析服务异常 | 5003 | 502 | SystemError |
| 数据库查询异常 | 5004 | 500 | SystemError |
| 业务操作不允许 | 4002 | 400 | BusinessError |
| 权限不足 | 3001 | 403 | AuthorizationError |

## 🎉 **总结**

通过这次修复，IP地理位置模块现在完全符合DDD架构的设计原则：

1. **✅ 领域异常**: 使用 `UnifiedBusinessException` 替代普通 `Error`
2. **✅ 业务上下文**: 包含丰富的业务信息和操作标识
3. **✅ 统一响应**: 所有错误都使用统一的响应格式
4. **✅ 完整追踪**: 支持requestId和traceId的全链路追踪
5. **✅ 错误分类**: 根据业务场景使用不同的错误码

这样既保持了DDD架构的纯净性，又提供了强大的错误处理和调试能力！🐱

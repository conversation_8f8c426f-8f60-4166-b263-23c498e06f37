import { Injectable, Optional } from '@nestjs/common';
import {
  HttpResponse,
  UnifiedResponse,
  EnhancedHttpResponse,
  EnhancedResponseOptions,
  SUCCESS_CODE,
  ERROR_CODE,
  SUCCESS_CODES,
  BUSINESS_ERROR_CODES,
  ErrorType,
  getHttpStatusCode,
  getErrorType
} from './http-response.interface';
import { LoggerService } from '../../common/logger/logger.service';
import { RequestTraceService } from './request-trace.service';

@Injectable()
export class HttpResponseResultService {
  constructor(
    @Optional() private readonly loggerService?: LoggerService,
    @Optional() private readonly requestTraceService?: RequestTraceService
  ) {}

  // ==================== 原有方法（保持不变，确保向后兼容） ====================

  /**
   * 成功响应
   * @param data 响应数据
   * @param msg 响应消息
   * @param code 状态码
   */
  success<T = any>(data?: T, msg = '操作成功', code = SUCCESS_CODE): HttpResponse<T> {
    return {
      code,
      msg,
      data: data as T,
    };
  }

  /**
   * 错误响应
   * @param msg 错误消息
   * @param code 状态码
   * @param data 错误数据
   */
  error<T = any>(msg = '系统错误', data?: T, code = ERROR_CODE): HttpResponse<T> {
    return {
      code,
      msg,
      data: data as T,
    };
  }

  /**
   * 自定义响应
   * @param code 状态码
   * @param msg 响应消息
   * @param data 响应数据
   */
  custom<T = any>(code: number, msg: string, data?: T): HttpResponse<T> {
    return {
      code,
      msg,
      data: data as T,
    };
  }

  // ==================== 新增增强方法（专为DDD模块设计） ====================

  /**
   * 增强的成功响应
   * 专为DDD模块设计，支持执行时间、缓存标识、时间戳等扩展信息
   * @param data 响应数据
   * @param msg 响应消息
   * @param code 状态码
   * @param options 增强选项
   */
  enhancedSuccess<T = any>(
    data?: T,
    msg = '操作成功',
    code = SUCCESS_CODE,
    options?: EnhancedResponseOptions & {
      logContext?: {
        module: string;
        operation: string;
        userId?: string;
        requestData?: any;
      }
    }
  ): EnhancedHttpResponse<T> {
    const response: EnhancedHttpResponse<T> = {
      code,
      msg,
      data: data as T,
      success: true,
    };

    // 默认包含时间戳，除非明确设置为false
    if (options?.includeTimestamp !== false) {
      response.timestamp = new Date().toISOString();
    }

    // 添加执行时间
    if (options?.executionTime !== undefined) {
      response.executionTime = `${options.executionTime}ms`;
    }

    // 添加缓存标识
    if (options?.fromCache !== undefined) {
      response.fromCache = options.fromCache;
    }

    // 添加元数据
    if (options?.meta) {
      response.meta = options.meta;
    }

    // 记录API响应日志
    if (options?.logContext && this.loggerService) {
      this.loggerService.logApiResponse(
        options.logContext.module,
        options.logContext.operation,
        {
          requestData: options.logContext.requestData,
          responseData: data,
          success: true,
          executionTime: options.executionTime,
          userId: options.logContext.userId,
          traceId: options.meta?.requestId
        }
      );
    }

    return response;
  }

  /**
   * 增强的错误响应
   * 专为DDD模块设计，支持执行时间、时间戳等扩展信息
   * @param msg 错误消息
   * @param data 错误数据
   * @param code 状态码
   * @param options 增强选项
   */
  enhancedError<T = any>(
    msg = '系统错误',
    data?: T,
    code = ERROR_CODE,
    options?: EnhancedResponseOptions & {
      logContext?: {
        module: string;
        operation: string;
        userId?: string;
        requestData?: any;
        error?: Error;
      }
    }
  ): EnhancedHttpResponse<T> {
    const response: EnhancedHttpResponse<T> = {
      code,
      msg,
      data: data as T,
      success: false,
    };

    // 默认包含时间戳，除非明确设置为false
    if (options?.includeTimestamp !== false) {
      response.timestamp = new Date().toISOString();
    }

    // 添加执行时间
    if (options?.executionTime !== undefined) {
      response.executionTime = `${options.executionTime}ms`;
    }

    // 添加元数据
    if (options?.meta) {
      response.meta = options.meta;
    }

    // 记录API错误响应日志
    if (options?.logContext && this.loggerService) {
      this.loggerService.logApiResponse(
        options.logContext.module,
        options.logContext.operation,
        {
          requestData: options.logContext.requestData,
          responseData: data,
          success: false,
          executionTime: options.executionTime,
          userId: options.logContext.userId,
          traceId: options.meta?.requestId,
          error: options.logContext.error
        }
      );
    }

    return response;
  }

  /**
   * 增强的自定义响应
   * @param code 状态码
   * @param msg 响应消息
   * @param data 响应数据
   * @param success 成功标识
   * @param options 增强选项
   */
  enhancedCustom<T = any>(
    code: number,
    msg: string,
    data?: T,
    success: boolean = true,
    options?: EnhancedResponseOptions
  ): EnhancedHttpResponse<T> {
    const response: EnhancedHttpResponse<T> = {
      code,
      msg,
      data: data as T,
      success,
    };

    // 默认包含时间戳，除非明确设置为false
    if (options?.includeTimestamp !== false) {
      response.timestamp = new Date().toISOString();
    }

    // 添加执行时间
    if (options?.executionTime !== undefined) {
      response.executionTime = `${options.executionTime}ms`;
    }

    // 添加缓存标识
    if (options?.fromCache !== undefined) {
      response.fromCache = options.fromCache;
    }

    // 添加元数据
    if (options?.meta) {
      response.meta = options.meta;
    }

    return response;
  }

  // ==================== 新增：统一响应方法（向后兼容） ====================

  /**
   * 统一成功响应
   * 扩展原有success方法，添加追踪信息和时间戳
   * 自动使用RequestTraceService获取追踪信息，确保请求ID的唯一性
   * @param data 响应数据
   * @param msg 响应消息
   * @param code 状态码
   * @param options 扩展选项（可选，会自动补充追踪信息）
   */
  unifiedSuccess<T = any>(
    data?: T,
    msg = '操作成功',
    code = SUCCESS_CODE,
    options?: {
      requestId?: string;
      traceId?: string;
      path?: string;
      executionTime?: number;
      fromCache?: boolean;
    }
  ): UnifiedResponse<T> {
    const response: UnifiedResponse<T> = {
      // 保持原有字段（向后兼容）
      code,
      msg,
      data: data as T,

      // 新增字段
      success: true,
      timestamp: new Date().toISOString(),
    };

    // 自动获取追踪信息（优先使用RequestTraceService）
    if (this.requestTraceService) {
      const traceInfo = this.requestTraceService.getTraceInfo();
      response.trace = {
        requestId: options?.requestId || traceInfo.requestId,
        traceId: options?.traceId || traceInfo.traceId,
        path: options?.path || traceInfo.path
      };

      // 如果没有提供执行时间，使用RequestTraceService的执行时间
      if (options?.executionTime === undefined) {
        (response as any).executionTime = `${traceInfo.executionTime}ms`;
      }
    } else {
      // 降级处理：使用传入的选项
      if (options?.requestId || options?.traceId || options?.path) {
        response.trace = {
          requestId: options.requestId,
          traceId: options.traceId,
          path: options.path
        };
      }
    }

    // 添加执行时间（兼容增强响应）
    if (options?.executionTime !== undefined) {
      (response as any).executionTime = `${options.executionTime}ms`;
    }

    // 添加缓存标识（兼容增强响应）
    if (options?.fromCache !== undefined) {
      (response as any).fromCache = options.fromCache;
    }

    return response;
  }

  /**
   * 统一错误响应
   * 扩展原有error方法，添加错误详情和追踪信息
   * 自动使用RequestTraceService获取追踪信息，确保请求ID的唯一性
   * @param msg 错误消息
   * @param data 错误数据
   * @param code 状态码
   * @param options 扩展选项（可选，会自动补充追踪信息）
   */
  unifiedError<T = any>(
    msg = '系统错误',
    data?: T,
    code = ERROR_CODE,
    options?: {
      requestId?: string;
      traceId?: string;
      path?: string;
      errorType?: ErrorType;
      errorDetails?: any;
      stack?: string;
      executionTime?: number;
    }
  ): UnifiedResponse<T> {
    const response: UnifiedResponse<T> = {
      // 保持原有字段（向后兼容）
      code,
      msg,
      data: data as T,

      // 新增字段
      success: false,
      timestamp: new Date().toISOString(),
    };

    // 自动获取追踪信息（优先使用RequestTraceService）
    if (this.requestTraceService) {
      const traceInfo = this.requestTraceService.getTraceInfo();
      response.trace = {
        requestId: options?.requestId || traceInfo.requestId,
        traceId: options?.traceId || traceInfo.traceId,
        path: options?.path || traceInfo.path
      };

      // 如果没有提供执行时间，使用RequestTraceService的执行时间
      if (options?.executionTime === undefined) {
        (response as any).executionTime = `${traceInfo.executionTime}ms`;
      }
    } else {
      // 降级处理：使用传入的选项
      if (options?.requestId || options?.traceId || options?.path) {
        response.trace = {
          requestId: options.requestId,
          traceId: options.traceId,
          path: options.path
        };
      }
    }

    // 添加错误详情
    if (options?.errorType || options?.errorDetails || options?.stack) {
      response.error = {
        type: options.errorType || getErrorType(code),
        details: options.errorDetails,
        stack: process.env.NODE_ENV === 'development' ? options.stack : undefined
      };
    }

    // 添加执行时间（兼容增强响应）
    if (options?.executionTime !== undefined) {
      (response as any).executionTime = `${options.executionTime}ms`;
    }

    return response;
  }

  /**
   * 业务错误响应
   * 使用预定义的业务错误码
   * @param businessCode 业务错误码
   * @param customMessage 自定义消息（可选）
   * @param data 错误数据
   * @param options 扩展选项
   */
  businessError<T = any>(
    businessCode: number,
    customMessage?: string,
    data?: T,
    options?: {
      requestId?: string;
      traceId?: string;
      path?: string;
      errorDetails?: any;
      executionTime?: number;
    }
  ): UnifiedResponse<T> {
    // 获取默认错误消息
    const defaultMessage = this.getBusinessErrorMessage(businessCode);

    return this.unifiedError(
      customMessage || defaultMessage,
      data,
      businessCode,
      {
        ...options,
        errorType: getErrorType(businessCode),
        errorDetails: options?.errorDetails
      }
    );
  }

  /**
   * 获取业务错误的默认消息
   */
  private getBusinessErrorMessage(code: number): string {
    const errorMessages: Record<number, string> = {
      // 用户相关
      [BUSINESS_ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
      [BUSINESS_ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
      [BUSINESS_ERROR_CODES.USER_DISABLED]: '用户已被禁用',
      [BUSINESS_ERROR_CODES.USER_LOCKED]: '用户已被锁定',

      // 认证相关
      [BUSINESS_ERROR_CODES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
      [BUSINESS_ERROR_CODES.TOKEN_INVALID]: '登录凭证无效',
      [BUSINESS_ERROR_CODES.LOGIN_FAILED]: '登录失败',
      [BUSINESS_ERROR_CODES.PASSWORD_INCORRECT]: '密码错误',
      [BUSINESS_ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定',

      // 权限相关
      [BUSINESS_ERROR_CODES.PERMISSION_DENIED]: '权限不足',
      [BUSINESS_ERROR_CODES.ROLE_NOT_FOUND]: '角色不存在',
      [BUSINESS_ERROR_CODES.INSUFFICIENT_PRIVILEGES]: '权限不足',

      // 业务逻辑相关
      [BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
      [BUSINESS_ERROR_CODES.OPERATION_NOT_ALLOWED]: '操作不被允许',
      [BUSINESS_ERROR_CODES.RESOURCE_LOCKED]: '资源已被锁定',
      [BUSINESS_ERROR_CODES.DUPLICATE_OPERATION]: '请勿重复操作',
      [BUSINESS_ERROR_CODES.RESOURCE_EXPIRED]: '资源已过期',

      // 系统相关
      [BUSINESS_ERROR_CODES.SYSTEM_MAINTENANCE]: '系统维护中',
      [BUSINESS_ERROR_CODES.RATE_LIMIT_EXCEEDED]: '操作过于频繁，请稍后重试',
      [BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR]: '外部服务异常',
      [BUSINESS_ERROR_CODES.DATABASE_ERROR]: '数据库异常',
      [BUSINESS_ERROR_CODES.CACHE_ERROR]: '缓存异常',
    };

    return errorMessages[code] || '系统错误';
  }
}

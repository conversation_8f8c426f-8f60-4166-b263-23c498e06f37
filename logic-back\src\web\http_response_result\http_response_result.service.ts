import { Injectable } from '@nestjs/common';
import {
  HttpResponse,
  EnhancedHttpResponse,
  EnhancedResponseOptions,
  SUCCESS_CODE,
  ERROR_CODE
} from './http-response.interface';
import { LoggerService } from '../../common/logger/logger.service';

@Injectable()
export class HttpResponseResultService {
  constructor(private readonly loggerService?: LoggerService) {}

  // ==================== 原有方法（保持不变，确保向后兼容） ====================

  /**
   * 成功响应
   * @param data 响应数据
   * @param msg 响应消息
   * @param code 状态码
   */
  success<T = any>(data?: T, msg = '操作成功', code = SUCCESS_CODE): HttpResponse<T> {
    return {
      code,
      msg,
      data: data as T,
    };
  }

  /**
   * 错误响应
   * @param msg 错误消息
   * @param code 状态码
   * @param data 错误数据
   */
  error<T = any>(msg = '系统错误', data?: T, code = ERROR_CODE): HttpResponse<T> {
    return {
      code,
      msg,
      data: data as T,
    };
  }

  /**
   * 自定义响应
   * @param code 状态码
   * @param msg 响应消息
   * @param data 响应数据
   */
  custom<T = any>(code: number, msg: string, data?: T): HttpResponse<T> {
    return {
      code,
      msg,
      data: data as T,
    };
  }

  // ==================== 新增增强方法（专为DDD模块设计） ====================

  /**
   * 增强的成功响应
   * 专为DDD模块设计，支持执行时间、缓存标识、时间戳等扩展信息
   * @param data 响应数据
   * @param msg 响应消息
   * @param code 状态码
   * @param options 增强选项
   */
  enhancedSuccess<T = any>(
    data?: T,
    msg = '操作成功',
    code = SUCCESS_CODE,
    options?: EnhancedResponseOptions & {
      logContext?: {
        module: string;
        operation: string;
        userId?: string;
        requestData?: any;
      }
    }
  ): EnhancedHttpResponse<T> {
    const response: EnhancedHttpResponse<T> = {
      code,
      msg,
      data: data as T,
      success: true,
    };

    // 默认包含时间戳，除非明确设置为false
    if (options?.includeTimestamp !== false) {
      response.timestamp = new Date().toISOString();
    }

    // 添加执行时间
    if (options?.executionTime !== undefined) {
      response.executionTime = `${options.executionTime}ms`;
    }

    // 添加缓存标识
    if (options?.fromCache !== undefined) {
      response.fromCache = options.fromCache;
    }

    // 添加元数据
    if (options?.meta) {
      response.meta = options.meta;
    }

    // 记录API响应日志
    if (options?.logContext && this.loggerService) {
      this.loggerService.logApiResponse(
        options.logContext.module,
        options.logContext.operation,
        {
          requestData: options.logContext.requestData,
          responseData: data,
          success: true,
          executionTime: options.executionTime,
          userId: options.logContext.userId,
          traceId: options.meta?.requestId
        }
      );
    }

    return response;
  }

  /**
   * 增强的错误响应
   * 专为DDD模块设计，支持执行时间、时间戳等扩展信息
   * @param msg 错误消息
   * @param data 错误数据
   * @param code 状态码
   * @param options 增强选项
   */
  enhancedError<T = any>(
    msg = '系统错误',
    data?: T,
    code = ERROR_CODE,
    options?: EnhancedResponseOptions & {
      logContext?: {
        module: string;
        operation: string;
        userId?: string;
        requestData?: any;
        error?: Error;
      }
    }
  ): EnhancedHttpResponse<T> {
    const response: EnhancedHttpResponse<T> = {
      code,
      msg,
      data: data as T,
      success: false,
    };

    // 默认包含时间戳，除非明确设置为false
    if (options?.includeTimestamp !== false) {
      response.timestamp = new Date().toISOString();
    }

    // 添加执行时间
    if (options?.executionTime !== undefined) {
      response.executionTime = `${options.executionTime}ms`;
    }

    // 添加元数据
    if (options?.meta) {
      response.meta = options.meta;
    }

    // 记录API错误响应日志
    if (options?.logContext && this.loggerService) {
      this.loggerService.logApiResponse(
        options.logContext.module,
        options.logContext.operation,
        {
          requestData: options.logContext.requestData,
          responseData: data,
          success: false,
          executionTime: options.executionTime,
          userId: options.logContext.userId,
          traceId: options.meta?.requestId,
          error: options.logContext.error
        }
      );
    }

    return response;
  }

  /**
   * 增强的自定义响应
   * @param code 状态码
   * @param msg 响应消息
   * @param data 响应数据
   * @param success 成功标识
   * @param options 增强选项
   */
  enhancedCustom<T = any>(
    code: number,
    msg: string,
    data?: T,
    success: boolean = true,
    options?: EnhancedResponseOptions
  ): EnhancedHttpResponse<T> {
    const response: EnhancedHttpResponse<T> = {
      code,
      msg,
      data: data as T,
      success,
    };

    // 默认包含时间戳，除非明确设置为false
    if (options?.includeTimestamp !== false) {
      response.timestamp = new Date().toISOString();
    }

    // 添加执行时间
    if (options?.executionTime !== undefined) {
      response.executionTime = `${options.executionTime}ms`;
    }

    // 添加缓存标识
    if (options?.fromCache !== undefined) {
      response.fromCache = options.fromCache;
    }

    // 添加元数据
    if (options?.meta) {
      response.meta = options.meta;
    }

    return response;
  }
}

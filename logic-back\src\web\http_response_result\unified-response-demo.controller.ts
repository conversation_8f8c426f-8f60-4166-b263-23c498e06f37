import { Controller, Get, Post, Body, Param, Query, Req } from '@nestjs/common';
import { Request } from 'express';
import { HttpResponseResultService } from './http_response_result.service';
import { UnifiedBusinessException } from '../../common/exceptions/unified-business.exception';
import { BUSINESS_ERROR_CODES } from './http-response.interface';
import { RequestIdUtils, BusinessModule } from '../../common/middleware/request-id.middleware';
import { ApiTags } from '@nestjs/swagger';

/**
 * 统一响应格式演示控制器
 * 展示如何使用新的统一响应格式，同时保持向后兼容
 */
@ApiTags('web/统一响应格式演示(unified-response-demo)')
@Controller('api/v1/demo')
export class UnifiedResponseDemoController {
  constructor(
    private readonly httpResponseService: HttpResponseResultService
  ) {}

  /**
   * 演示原有响应格式（向后兼容）
   */
  @Get('legacy-success')
  async legacySuccess() {
    // 原有方法继续可用
    return this.httpResponseService.success(
      { message: 'This is legacy format' },
      '操作成功'
    );
    // 返回: { code: 200, msg: '操作成功', data: {...} }
  }

  /**
   * 演示原有错误格式（向后兼容）
   */
  @Get('legacy-error')
  async legacyError() {
    // 原有方法继续可用
    return this.httpResponseService.error(
      '这是原有的错误格式',
      { errorCode: 'DEMO_ERROR' },
      400
    );
    // 返回: { code: 400, msg: '这是原有的错误格式', data: {...} }
  }

  /**
   * 演示统一成功响应格式
   */
  @Get('unified-success')
  async unifiedSuccess(@Req() request: Request) {
    const requestId = this.generateRequestId();
    const traceId = (request as any).traceId || this.generateTraceId();

    // 使用新的统一响应方法
    return this.httpResponseService.unifiedSuccess(
      { 
        message: 'This is unified format',
        features: ['success flag', 'timestamp', 'trace info']
      },
      '统一格式操作成功',
      200,
      {
        requestId,
        traceId,
        path: request.url,
        executionTime: 25,
        fromCache: false
      }
    );
    // 返回: { code: 200, msg: '...', data: {...}, success: true, timestamp: '...', trace: {...} }
  }

  /**
   * 演示统一错误响应格式
   */
  @Get('unified-error')
  async unifiedError(@Req() request: Request) {
    const requestId = this.generateRequestId();
    const traceId = (request as any).traceId || this.generateTraceId();

    // 使用新的统一错误响应方法
    return this.httpResponseService.unifiedError(
      '这是统一格式的错误响应',
      { errorCode: 'DEMO_ERROR' },
      400,
      {
        requestId,
        traceId,
        path: request.url,
        errorDetails: { reason: 'This is a demo error' },
        executionTime: 15
      }
    );
    // 返回: { code: 400, msg: '...', data: {...}, success: false, timestamp: '...', trace: {...}, error: {...} }
  }

  /**
   * 演示业务错误响应
   */
  @Get('business-error/:code')
  async businessError(
    @Param('code') code: string,
    @Req() request: Request
  ) {
    const requestId = this.generateRequestId();
    const businessCode = parseInt(code);

    // 使用业务错误响应方法
    return this.httpResponseService.businessError(
      businessCode,
      undefined, // 使用默认消息
      { demoParam: code },
      {
        requestId,
        path: request.url,
        errorDetails: { 
          requestedCode: businessCode,
          availableCodes: Object.values(BUSINESS_ERROR_CODES)
        }
      }
    );
  }

  /**
   * 演示抛出统一业务异常
   */
  @Get('throw-exception/:type')
  async throwException(@Param('type') type: string) {
    switch (type) {
      case 'user-not-found':
        throw UnifiedBusinessException.userNotFound(12345, {
          searchMethod: 'findById',
          searchedAt: new Date()
        });

      case 'permission-denied':
        throw UnifiedBusinessException.permissionDenied(
          'demo-resource',
          'read',
          { requiredRole: 'admin', userRole: 'user' }
        );

      case 'token-expired':
        throw UnifiedBusinessException.tokenExpired({
          expiredAt: new Date(),
          tokenType: 'access_token'
        });

      case 'insufficient-balance':
        throw UnifiedBusinessException.insufficientBalance(
          100,
          500,
          { currency: 'CNY', accountId: 'demo-account' }
        );

      case 'duplicate-operation':
        throw UnifiedBusinessException.duplicateOperation(
          'create-user',
          { previousOperationId: 'op-123', interval: '5s' }
        );

      case 'rate-limit':
        throw UnifiedBusinessException.rateLimitExceeded(
          60,
          { limit: 100, window: '1h', current: 101 }
        );

      default:
        throw new UnifiedBusinessException(
          5000,
          '未知的异常类型',
          { requestedType: type },
          { availableTypes: ['user-not-found', 'permission-denied', 'token-expired'] }
        );
    }
  }

  /**
   * 演示增强响应格式（兼容现有的增强响应）
   */
  @Post('enhanced-demo')
  async enhancedDemo(@Body() body: any, @Req() request: Request) {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      // 模拟业务处理
      await this.simulateAsyncOperation();

      // 使用增强响应方法（保持兼容）
      return this.httpResponseService.enhancedSuccess(
        { 
          processed: body,
          result: 'Enhanced response with unified features'
        },
        '增强响应处理成功',
        200,
        {
          executionTime: Date.now() - startTime,
          fromCache: false,
          includeTimestamp: true,
          meta: {
            requestId,
            version: 'v2.0.0',
            features: ['enhanced', 'unified', 'backward-compatible']
          },
          logContext: {
            module: 'Demo',
            operation: 'enhancedDemo',
            requestData: body
          }
        }
      );
    } catch (error) {
      return this.httpResponseService.enhancedError(
        '增强响应处理失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: { requestId },
          logContext: {
            module: 'Demo',
            operation: 'enhancedDemo',
            requestData: body,
            error: error
          }
        }
      );
    }
  }

  /**
   * 演示业务模块requestId
   */
  @Get('business-request-id')
  async businessRequestIdDemo(@Req() request: Request) {
    // 获取请求中的业务模块信息
    const requestId = RequestIdUtils.getRequestId(request);
    const businessModule = (request as any).businessModule;
    const modulePrefix = RequestIdUtils.extractModulePrefix(requestId);

    // 演示不同业务模块的requestId生成
    const examples = {
      current: {
        requestId,
        businessModule,
        modulePrefix,
        isFromDemo: RequestIdUtils.isFromModule(requestId, BusinessModule.GENERAL)
      },
      examples: {
        ipLocation: RequestIdUtils.generateBusinessRequestId(BusinessModule.IP_LOCATION),
        payment: RequestIdUtils.generateBusinessRequestId(BusinessModule.PAYMENT),
        user: RequestIdUtils.generateBusinessRequestId(BusinessModule.USER),
        auth: RequestIdUtils.generateBusinessRequestId(BusinessModule.AUTH),
        course: RequestIdUtils.generateBusinessRequestId(BusinessModule.COURSE),
        general: RequestIdUtils.generateBusinessRequestId(BusinessModule.GENERAL)
      },
      pathInference: {
        '/api/v1/ip-location/query': RequestIdUtils.inferBusinessModule('/api/v1/ip-location/query'),
        '/api/v1/payment/create': RequestIdUtils.inferBusinessModule('/api/v1/payment/create'),
        '/api/v1/user/profile': RequestIdUtils.inferBusinessModule('/api/v1/user/profile'),
        '/api/v1/auth/login': RequestIdUtils.inferBusinessModule('/api/v1/auth/login'),
        '/api/v1/course/list': RequestIdUtils.inferBusinessModule('/api/v1/course/list'),
        '/api/v1/unknown/endpoint': RequestIdUtils.inferBusinessModule('/api/v1/unknown/endpoint')
      }
    };

    return this.httpResponseService.unifiedSuccess(
      examples,
      '业务模块requestId演示',
      200,
      {
        requestId,
        path: request.url
      }
    );
  }

  /**
   * 演示响应格式对比
   */
  @Get('format-comparison')
  async formatComparison(@Query('format') format: string = 'unified') {
    const data = {
      message: 'Response format comparison',
      timestamp: new Date(),
      features: ['flexible', 'compatible', 'enhanced']
    };

    switch (format) {
      case 'legacy':
        return this.httpResponseService.success(data, '原有格式');

      case 'enhanced':
        return this.httpResponseService.enhancedSuccess(
          data,
          '增强格式',
          200,
          {
            executionTime: 10,
            fromCache: false,
            meta: { version: 'v2.0.0' }
          }
        );

      case 'unified':
      default:
        return this.httpResponseService.unifiedSuccess(
          data,
          '统一格式',
          200,
          {
            requestId: this.generateRequestId(),
            path: '/api/v1/demo/format-comparison',
            executionTime: 10
          }
        );
    }
  }

  // ==================== 私有辅助方法 ====================

  private generateRequestId(): string {
    return `demo-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  private async simulateAsyncOperation(): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, Math.random() * 100));
  }
}

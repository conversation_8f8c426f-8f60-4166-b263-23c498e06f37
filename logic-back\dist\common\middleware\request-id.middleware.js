"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestIdUtils = exports.RequestIdMiddleware = void 0;
const common_1 = require("@nestjs/common");
let RequestIdMiddleware = class RequestIdMiddleware {
    use(req, res, next) {
        const requestId = this.generateRequestId();
        const traceId = this.generateTraceId();
        req.requestId = requestId;
        req.traceId = traceId;
        res.setHeader('X-Request-ID', requestId);
        res.setHeader('X-Trace-ID', traceId);
        next();
    }
    generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
};
exports.RequestIdMiddleware = RequestIdMiddleware;
exports.RequestIdMiddleware = RequestIdMiddleware = __decorate([
    (0, common_1.Injectable)()
], RequestIdMiddleware);
class RequestIdUtils {
    static getRequestId(req) {
        return req.requestId || RequestIdUtils.generateRequestId();
    }
    static getTraceId(req) {
        return req.traceId || RequestIdUtils.generateTraceId();
    }
    static generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    static generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    static generateBusinessRequestId(prefix) {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
}
exports.RequestIdUtils = RequestIdUtils;
//# sourceMappingURL=request-id.middleware.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestIdUtils = exports.BusinessModule = exports.RequestIdMiddleware = void 0;
const common_1 = require("@nestjs/common");
let RequestIdMiddleware = class RequestIdMiddleware {
    use(req, res, next) {
        const businessModule = RequestIdUtils.inferBusinessModule(req.url);
        const requestId = RequestIdUtils.generateBusinessRequestId(businessModule);
        const traceId = this.generateTraceId();
        req.requestId = requestId;
        req.traceId = traceId;
        req.businessModule = businessModule;
        res.setHeader('X-Request-ID', requestId);
        res.setHeader('X-Trace-ID', traceId);
        res.setHeader('X-Business-Module', businessModule);
        next();
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
};
exports.RequestIdMiddleware = RequestIdMiddleware;
exports.RequestIdMiddleware = RequestIdMiddleware = __decorate([
    (0, common_1.Injectable)()
], RequestIdMiddleware);
var BusinessModule;
(function (BusinessModule) {
    BusinessModule["IP_LOCATION"] = "ip-loc";
    BusinessModule["PAYMENT"] = "payment";
    BusinessModule["USER"] = "user";
    BusinessModule["AUTH"] = "auth";
    BusinessModule["COURSE"] = "course";
    BusinessModule["TASK"] = "task";
    BusinessModule["PACKAGE"] = "package";
    BusinessModule["SCRATCH"] = "scratch";
    BusinessModule["TPS"] = "tps";
    BusinessModule["GENERAL"] = "req";
})(BusinessModule || (exports.BusinessModule = BusinessModule = {}));
class RequestIdUtils {
    static getRequestId(req) {
        return req.requestId || RequestIdUtils.generateRequestId();
    }
    static getTraceId(req) {
        return req.traceId || RequestIdUtils.generateTraceId();
    }
    static getBusinessRequestId(req, fallbackModule) {
        const existingId = req.requestId;
        if (existingId) {
            return existingId;
        }
        const businessModule = fallbackModule || RequestIdUtils.inferBusinessModule(req.url);
        return RequestIdUtils.generateBusinessRequestId(businessModule);
    }
    static generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    static generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    static generateBusinessRequestId(module) {
        return `${module}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    static inferBusinessModule(path) {
        if (!path)
            return BusinessModule.GENERAL;
        const cleanPath = path.split('?')[0].toLowerCase();
        if (cleanPath.includes('/ip-location'))
            return BusinessModule.IP_LOCATION;
        if (cleanPath.includes('/payment'))
            return BusinessModule.PAYMENT;
        if (cleanPath.includes('/user'))
            return BusinessModule.USER;
        if (cleanPath.includes('/auth') || cleanPath.includes('/login'))
            return BusinessModule.AUTH;
        if (cleanPath.includes('/course'))
            return BusinessModule.COURSE;
        if (cleanPath.includes('/task'))
            return BusinessModule.TASK;
        if (cleanPath.includes('/package'))
            return BusinessModule.PACKAGE;
        if (cleanPath.includes('/scratch'))
            return BusinessModule.SCRATCH;
        if (cleanPath.includes('/tps'))
            return BusinessModule.TPS;
        return BusinessModule.GENERAL;
    }
    static isFromModule(requestId, module) {
        return requestId.startsWith(`${module}-`);
    }
    static extractModulePrefix(requestId) {
        const parts = requestId.split('-');
        return parts.length > 0 ? parts[0] : 'unknown';
    }
}
exports.RequestIdUtils = RequestIdUtils;
//# sourceMappingURL=request-id.middleware.js.map
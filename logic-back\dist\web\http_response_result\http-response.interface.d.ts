export interface HttpResponse<T = any> {
    code: number;
    msg: string;
    data: T;
}
export interface UnifiedResponse<T = any> extends HttpResponse<T> {
    success: boolean;
    timestamp: string;
    trace?: {
        requestId?: string;
        traceId?: string;
        path?: string;
    };
    error?: {
        type: string;
        details?: any;
        stack?: string;
    };
}
export interface EnhancedHttpResponse<T = any> extends HttpResponse<T> {
    success?: boolean;
    timestamp?: string;
    executionTime?: string;
    fromCache?: boolean;
    meta?: {
        requestId?: string;
        version?: string;
        [key: string]: any;
    };
}
export interface EnhancedResponseOptions {
    executionTime?: number;
    fromCache?: boolean;
    includeTimestamp?: boolean;
    meta?: {
        requestId?: string;
        version?: string;
        [key: string]: any;
    };
}
export declare const SUCCESS_CODE = 200;
export declare const ERROR_CODE = 500;
export declare const BAD_REQUEST_CODE = 400;
export declare const UNAUTHORIZED_CODE = 401;
export declare const FORBIDDEN_CODE = 403;
export declare const NOT_FOUND_CODE = 404;
export declare const SUCCESS_CODES: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly ACCEPTED: 202;
    readonly PARTIAL_SUCCESS: 206;
};
export declare const BUSINESS_ERROR_CODES: {
    readonly USER_NOT_FOUND: 1001;
    readonly USER_ALREADY_EXISTS: 1002;
    readonly USER_DISABLED: 1003;
    readonly USER_LOCKED: 1004;
    readonly TOKEN_EXPIRED: 2001;
    readonly TOKEN_INVALID: 2002;
    readonly LOGIN_FAILED: 2003;
    readonly PASSWORD_INCORRECT: 2004;
    readonly ACCOUNT_LOCKED: 2005;
    readonly PERMISSION_DENIED: 3001;
    readonly ROLE_NOT_FOUND: 3002;
    readonly INSUFFICIENT_PRIVILEGES: 3003;
    readonly INSUFFICIENT_BALANCE: 4001;
    readonly OPERATION_NOT_ALLOWED: 4002;
    readonly RESOURCE_LOCKED: 4003;
    readonly DUPLICATE_OPERATION: 4004;
    readonly RESOURCE_EXPIRED: 4005;
    readonly SYSTEM_MAINTENANCE: 5001;
    readonly RATE_LIMIT_EXCEEDED: 5002;
    readonly EXTERNAL_SERVICE_ERROR: 5003;
    readonly DATABASE_ERROR: 5004;
    readonly CACHE_ERROR: 5005;
};
export declare enum ErrorType {
    SYSTEM_ERROR = "SystemError",
    BUSINESS_ERROR = "BusinessError",
    VALIDATION_ERROR = "ValidationError",
    AUTHENTICATION_ERROR = "AuthenticationError",
    AUTHORIZATION_ERROR = "AuthorizationError",
    NETWORK_ERROR = "NetworkError",
    DATABASE_ERROR = "DatabaseError",
    EXTERNAL_SERVICE_ERROR = "ExternalServiceError"
}
export declare const CODE_TO_HTTP_STATUS: Record<number, number>;
export declare function getHttpStatusCode(businessCode: number): number;
export declare function getErrorType(code: number): ErrorType;

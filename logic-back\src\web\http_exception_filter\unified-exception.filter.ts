import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
import { LoggerService } from '../../common/logger/logger.service';
import { RequestIdUtils } from '../../common/middleware/request-id.middleware';
import {
  UnifiedResponse,
  ErrorType,
  getHttpStatusCode,
  getErrorType,
  BUSINESS_ERROR_CODES
} from '../http_response_result/http-response.interface';

/**
 * 统一异常过滤器
 * 向后兼容现有的HttpExceptionFilter，同时提供增强的错误处理
 */
@Catch()
export class UnifiedExceptionFilter implements ExceptionFilter {
  constructor(
    private readonly httpResponseResultService: HttpResponseResultService,
    private readonly loggerService: LoggerService
  ) {}
  
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    
    // 获取追踪信息（优先使用请求中的ID，确保一致性）
    const requestId = RequestIdUtils.getRequestId(request);
    const traceId = RequestIdUtils.getTraceId(request);
    const path = request.url;
    
    // 解析异常信息
    const exceptionInfo = this.parseException(exception);
    
    // 记录错误日志
    this.logError(exception, request, exceptionInfo, traceId);
    
    // 构建统一响应
    const errorResponse = this.buildErrorResponse(
      exceptionInfo,
      requestId,
      traceId,
      path
    );
    
    // 获取HTTP状态码
    const httpStatus = getHttpStatusCode(exceptionInfo.code);
    
    // 返回响应
    response
      .status(httpStatus)
      .json(errorResponse);
  }

  /**
   * 解析异常信息
   */
  private parseException(exception: unknown): {
    code: number;
    message: string;
    data: any;
    type: ErrorType;
    stack?: string;
    details?: any;
  } {
    let code = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = '服务器内部错误';
    let data = null;
    let details: any = {};
    let stack: string | undefined;

    if (exception instanceof HttpException) {
      code = exception.getStatus();
      const exceptionResponse = exception.getResponse();
      
      if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
        const exceptionObj = exceptionResponse as any;
        
        // 优先使用自定义消息和代码
        if (exceptionObj.msg) {
          message = exceptionObj.msg;
        } else if (exceptionObj.message) {
          message = exceptionObj.message;
        } else {
          message = exception.message;
        }
        
        if (exceptionObj.code) {
          code = exceptionObj.code;
        }
        
        if (exceptionObj.data !== undefined) {
          data = exceptionObj.data;
        }
        
        details = exceptionObj;
      } else if (typeof exceptionResponse === 'string') {
        message = exceptionResponse;
      } else {
        message = exception.message;
      }
    } else if (exception instanceof Error) {
      message = exception.message;
      stack = exception.stack;
      details = {
        name: exception.name,
        message: exception.message
      };
    }

    // 根据状态码设置默认消息（保持向后兼容）
    if (message === '服务器内部错误' || !message) {
      message = this.getDefaultErrorMessage(code);
    }

    return {
      code,
      message,
      data,
      type: getErrorType(code),
      stack,
      details
    };
  }

  /**
   * 构建错误响应
   */
  private buildErrorResponse(
    exceptionInfo: any,
    requestId: string,
    traceId: string,
    path: string
  ): UnifiedResponse<any> {
    return this.httpResponseResultService.unifiedError(
      exceptionInfo.message,
      exceptionInfo.data,
      exceptionInfo.code,
      {
        requestId,
        traceId,
        path,
        errorType: exceptionInfo.type,
        errorDetails: exceptionInfo.details,
        stack: exceptionInfo.stack
      }
    );
  }

  /**
   * 记录错误日志
   */
  private logError(
    exception: unknown,
    request: Request,
    exceptionInfo: any,
    traceId: string
  ): void {
    const logContext = {
      traceId,
      method: request.method,
      url: request.url,
      userAgent: request.get('User-Agent'),
      ip: request.ip || request.connection?.remoteAddress,
      code: exceptionInfo.code,
      message: exceptionInfo.message,
      type: exceptionInfo.type
    };

    if (exceptionInfo.code >= 500) {
      // 服务器错误 - ERROR级别
      this.loggerService.error(
        `[${traceId}] Server Error: ${request.method} ${request.url} - ${exceptionInfo.message}`,
        exception instanceof Error ? exception.stack : JSON.stringify(logContext),
        'UnifiedExceptionFilter'
      );
    } else if (exceptionInfo.code >= 400) {
      // 客户端错误 - WARN级别
      this.loggerService.warn(
        `[${traceId}] Client Error: ${request.method} ${request.url} - ${exceptionInfo.message}`,
        'UnifiedExceptionFilter'
      );
    } else {
      // 其他错误 - INFO级别
      this.loggerService.log(
        `[${traceId}] Exception: ${request.method} ${request.url} - ${exceptionInfo.message}`,
        'UnifiedExceptionFilter'
      );
    }
  }

  /**
   * 获取默认错误消息（保持向后兼容）
   */
  private getDefaultErrorMessage(code: number): string {
    switch (code) {
      case HttpStatus.UNAUTHORIZED:
        return '请先登录后再访问';
      case HttpStatus.FORBIDDEN:
        return '您没有权限执行此操作';
      case HttpStatus.NOT_FOUND:
        return '请求的资源不存在';
      case HttpStatus.BAD_REQUEST:
        return '请求参数错误';
      case HttpStatus.CONFLICT:
        return '资源冲突';
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return '数据验证失败';
      case HttpStatus.TOO_MANY_REQUESTS:
        return '请求过于频繁，请稍后重试';
      case HttpStatus.INTERNAL_SERVER_ERROR:
        return '服务器内部错误';
      case HttpStatus.BAD_GATEWAY:
        return '网关错误';
      case HttpStatus.SERVICE_UNAVAILABLE:
        return '服务暂时不可用';
      case HttpStatus.GATEWAY_TIMEOUT:
        return '网关超时';
      default:
        if (code >= 500) {
          return '服务器错误';
        } else if (code >= 400) {
          return '请求错误';
        } else {
          return '未知错误';
        }
    }
  }

  // 注意：ID生成方法已移至RequestIdUtils工具类，确保全局一致性
}

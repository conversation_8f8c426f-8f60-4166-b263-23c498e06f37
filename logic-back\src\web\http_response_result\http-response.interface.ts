/**
 * 响应体接口定义（基础版本，保持向后兼容）
 */
export interface HttpResponse<T = any> {
  /** 状态码 */
  code: number;
  /** 消息 */
  msg: string;
  /** 数据 */
  data: T;
}

/**
 * 统一响应接口（扩展版本，向后兼容HttpResponse）
 */
export interface UnifiedResponse<T = any> extends HttpResponse<T> {
  /** 成功标识 */
  success: boolean;

  /** 时间戳 */
  timestamp: string;

  /** 追踪信息（可选） */
  trace?: {
    /** 请求ID */
    requestId?: string;
    /** 追踪ID */
    traceId?: string;
    /** 请求路径 */
    path?: string;
  };

  /** 错误详情（仅错误响应） */
  error?: {
    /** 错误类型 */
    type: string;
    /** 错误详情 */
    details?: any;
    /** 错误堆栈（仅开发环境） */
    stack?: string;
  };
}

/**
 * 增强的响应体接口定义
 * 扩展原有接口，支持DDD模块的特殊需求
 * 完全向后兼容原有的HttpResponse接口
 */
export interface EnhancedHttpResponse<T = any> extends HttpResponse<T> {
  /** 成功标识 */
  success?: boolean;
  /** 时间戳 */
  timestamp?: string;
  /** 执行时间 */
  executionTime?: string;
  /** 是否来自缓存 */
  fromCache?: boolean;
  /** 元数据信息 */
  meta?: {
    /** 请求ID */
    requestId?: string;
    /** 版本信息 */
    version?: string;
    /** 其他扩展信息 */
    [key: string]: any;
  };
}

/**
 * 增强响应选项接口
 */
export interface EnhancedResponseOptions {
  /** 执行时间（毫秒） */
  executionTime?: number;
  /** 是否来自缓存 */
  fromCache?: boolean;
  /** 是否包含时间戳 */
  includeTimestamp?: boolean;
  /** 元数据信息 */
  meta?: {
    requestId?: string;
    version?: string;
    [key: string]: any;
  };
}

/**
 * 请求成功的状态码
 */
export const SUCCESS_CODE = 200;

/**
 * 系统错误的状态码
 */
export const ERROR_CODE = 500;

/**
 * 请求参数错误的状态码
 */
export const BAD_REQUEST_CODE = 400;

/**
 * 未授权的状态码
 */
export const UNAUTHORIZED_CODE = 401;

/**
 * 权限不足的状态码
 */
export const FORBIDDEN_CODE = 403;

/**
 * 资源不存在的状态码
 */
export const NOT_FOUND_CODE = 404;

// ==================== 新增：统一错误响应相关定义 ====================

/**
 * 成功状态码
 */
export const SUCCESS_CODES = {
  OK: 200,                    // 操作成功
  CREATED: 201,              // 创建成功
  ACCEPTED: 202,             // 已接受处理
  PARTIAL_SUCCESS: 206,      // 部分成功
} as const;

/**
 * 业务错误状态码
 */
export const BUSINESS_ERROR_CODES = {
  // 用户相关 (1000-1999)
  USER_NOT_FOUND: 1001,
  USER_ALREADY_EXISTS: 1002,
  USER_DISABLED: 1003,
  USER_LOCKED: 1004,

  // 认证相关 (2000-2999)
  TOKEN_EXPIRED: 2001,
  TOKEN_INVALID: 2002,
  LOGIN_FAILED: 2003,
  PASSWORD_INCORRECT: 2004,
  ACCOUNT_LOCKED: 2005,

  // 权限相关 (3000-3999)
  PERMISSION_DENIED: 3001,
  ROLE_NOT_FOUND: 3002,
  INSUFFICIENT_PRIVILEGES: 3003,

  // 业务逻辑相关 (4000-4999)
  INSUFFICIENT_BALANCE: 4001,
  OPERATION_NOT_ALLOWED: 4002,
  RESOURCE_LOCKED: 4003,
  DUPLICATE_OPERATION: 4004,
  RESOURCE_EXPIRED: 4005,

  // 系统相关 (5000-5999)
  SYSTEM_MAINTENANCE: 5001,
  RATE_LIMIT_EXCEEDED: 5002,
  EXTERNAL_SERVICE_ERROR: 5003,
  DATABASE_ERROR: 5004,
  CACHE_ERROR: 5005,
} as const;

/**
 * 错误类型枚举
 */
export enum ErrorType {
  SYSTEM_ERROR = 'SystemError',
  BUSINESS_ERROR = 'BusinessError',
  VALIDATION_ERROR = 'ValidationError',
  AUTHENTICATION_ERROR = 'AuthenticationError',
  AUTHORIZATION_ERROR = 'AuthorizationError',
  NETWORK_ERROR = 'NetworkError',
  DATABASE_ERROR = 'DatabaseError',
  EXTERNAL_SERVICE_ERROR = 'ExternalServiceError',
}

/**
 * 状态码到HTTP状态码的映射
 */
export const CODE_TO_HTTP_STATUS: Record<number, number> = {
  // 成功状态码
  200: 200, 201: 201, 202: 202, 206: 206,

  // 标准HTTP错误码
  400: 400, 401: 401, 403: 403, 404: 404, 405: 405, 409: 409, 422: 422, 429: 429,
  500: 500, 501: 501, 502: 502, 503: 503, 504: 504,

  // 业务错误码映射
  1001: 404, 1002: 409, 1003: 403, 1004: 423,  // 用户相关
  2001: 401, 2002: 401, 2003: 401, 2004: 401, 2005: 423,  // 认证相关
  3001: 403, 3002: 403, 3003: 403,  // 权限相关
  4001: 400, 4002: 400, 4003: 423, 4004: 409, 4005: 410,  // 业务逻辑
  5001: 503, 5002: 429, 5003: 502, 5004: 500, 5005: 500,  // 系统相关
};

/**
 * 获取HTTP状态码
 */
export function getHttpStatusCode(businessCode: number): number {
  return CODE_TO_HTTP_STATUS[businessCode] || 500;
}

/**
 * 获取错误类型
 */
export function getErrorType(code: number): ErrorType {
  if (code >= 1000 && code < 2000) return ErrorType.BUSINESS_ERROR;
  if (code >= 2000 && code < 3000) return ErrorType.AUTHENTICATION_ERROR;
  if (code >= 3000 && code < 4000) return ErrorType.AUTHORIZATION_ERROR;
  if (code >= 4000 && code < 5000) return ErrorType.BUSINESS_ERROR;
  if (code >= 5000 && code < 6000) return ErrorType.SYSTEM_ERROR;
  if (code === 400 || code === 422) return ErrorType.VALIDATION_ERROR;
  if (code === 401) return ErrorType.AUTHENTICATION_ERROR;
  if (code === 403) return ErrorType.AUTHORIZATION_ERROR;
  if (code >= 500) return ErrorType.SYSTEM_ERROR;

  return ErrorType.SYSTEM_ERROR;
}
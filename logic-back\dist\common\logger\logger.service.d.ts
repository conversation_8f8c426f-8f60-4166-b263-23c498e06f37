import { LoggerService as NestLoggerService } from '@nestjs/common';
import { Logger } from 'winston';
export declare class LoggerService implements NestLoggerService {
    private readonly logger;
    constructor(logger: Logger);
    log(message: any, context?: string): void;
    error(message: any, trace?: string, context?: string): void;
    warn(message: any, context?: string): void;
    debug(message: any, context?: string): void;
    verbose(message: any, context?: string): void;
    logHttpRequest(req: any, res: any, responseTime: number): void;
    logHttpRequestResponse(req: any, res: any, responseTime: number, details: {
        requestBody?: any;
        requestQuery?: any;
        responseBody?: any;
        traceId?: string;
    }): void;
    logDatabase(operation: string, table: string, data?: any, error?: any): void;
    logPayment(action: string, data: any, error?: any): void;
    logAuth(action: string, userId?: string, details?: any, error?: any): void;
    logBusiness(module: string, action: string, data?: any, error?: any): void;
    logStartup(message: string, details?: any): void;
    logApiResponse(module: string, operation: string, details: {
        requestData?: any;
        responseData?: any;
        success: boolean;
        executionTime?: number;
        userId?: string;
        traceId?: string;
        error?: Error;
    }): void;
    private generateTraceId;
    private sanitizeRequestData;
    private sanitizeResponseData;
    private sanitizeLogData;
    private recursiveSanitize;
    logPerformance(operation: string, duration: number, details?: any): void;
    logSecurity(event: string, details: any, severity?: 'low' | 'medium' | 'high'): void;
}

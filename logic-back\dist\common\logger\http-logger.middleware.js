"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnhancedHttpLoggerMiddleware = exports.HttpLoggerMiddleware = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("./logger.service");
let HttpLoggerMiddleware = class HttpLoggerMiddleware {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    use(req, res, next) {
        const startTime = Date.now();
        const traceId = this.generateTraceId();
        req.traceId = traceId;
        this.loggerService.debug(`[${traceId}] Incoming ${req.method} ${req.url}`, 'HTTP');
        res.on('finish', () => {
            const responseTime = Date.now() - startTime;
            this.loggerService.logHttpRequest(req, res, responseTime);
        });
        next();
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
};
exports.HttpLoggerMiddleware = HttpLoggerMiddleware;
exports.HttpLoggerMiddleware = HttpLoggerMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], HttpLoggerMiddleware);
let EnhancedHttpLoggerMiddleware = class EnhancedHttpLoggerMiddleware {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    use(req, res, next) {
        const startTime = Date.now();
        const traceId = this.generateTraceId();
        req.traceId = traceId;
        this.loggerService.debug(`[${traceId}] Enhanced Incoming ${req.method} ${req.url}`, 'HTTP_ENHANCED');
        const requestBody = req.body;
        const requestQuery = req.query;
        let responseBody;
        const originalSend = res.send;
        const originalJson = res.json;
        res.send = function (body) {
            responseBody = body;
            return originalSend.call(this, body);
        };
        res.json = function (body) {
            responseBody = body;
            return originalJson.call(this, body);
        };
        res.on('finish', () => {
            const responseTime = Date.now() - startTime;
            this.loggerService.logHttpRequestResponse(req, res, responseTime, {
                requestBody,
                requestQuery,
                responseBody,
                traceId
            });
        });
        next();
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
};
exports.EnhancedHttpLoggerMiddleware = EnhancedHttpLoggerMiddleware;
exports.EnhancedHttpLoggerMiddleware = EnhancedHttpLoggerMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], EnhancedHttpLoggerMiddleware);
//# sourceMappingURL=http-logger.middleware.js.map
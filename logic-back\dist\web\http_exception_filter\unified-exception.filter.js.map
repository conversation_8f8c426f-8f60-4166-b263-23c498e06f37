{"version": 3, "file": "unified-exception.filter.js", "sourceRoot": "", "sources": ["../../../src/web/http_exception_filter/unified-exception.filter.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAAkG;AAElG,uGAAiG;AACjG,uEAAmE;AACnE,yFAA+E;AAC/E,6FAMyD;AAOlD,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAEd;IACA;IAFnB,YACmB,yBAAoD,EACpD,aAA4B;QAD5B,8BAAyB,GAAzB,yBAAyB,CAA2B;QACpD,kBAAa,GAAb,aAAa,CAAe;IAC5C,CAAC;IAEJ,KAAK,CAAC,SAAkB,EAAE,IAAmB;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,EAAW,CAAC;QAG1C,MAAM,SAAS,GAAG,sCAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,sCAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QACnD,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;QAGzB,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAGrD,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAG1D,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAC3C,aAAa,EACb,SAAS,EACT,OAAO,EACP,IAAI,CACL,CAAC;QAGF,MAAM,UAAU,GAAG,IAAA,2CAAiB,EAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAGzD,QAAQ;aACL,MAAM,CAAC,UAAU,CAAC;aAClB,IAAI,CAAC,aAAa,CAAC,CAAC;IACzB,CAAC;IAKO,cAAc,CAAC,SAAkB;QAQvC,IAAI,IAAI,GAAG,mBAAU,CAAC,qBAAqB,CAAC;QAC5C,IAAI,OAAO,GAAG,SAAS,CAAC;QACxB,IAAI,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,OAAO,GAAQ,EAAE,CAAC;QACtB,IAAI,KAAyB,CAAC;QAE9B,IAAI,SAAS,YAAY,sBAAa,EAAE,CAAC;YACvC,IAAI,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAC7B,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YAElD,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBACxE,MAAM,YAAY,GAAG,iBAAwB,CAAC;gBAG9C,IAAI,YAAY,CAAC,GAAG,EAAE,CAAC;oBACrB,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC;gBAC7B,CAAC;qBAAM,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;oBAChC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;gBACjC,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;gBAC9B,CAAC;gBAED,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;oBACtB,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;gBAC3B,CAAC;gBAED,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;oBACpC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;gBAC3B,CAAC;gBAED,OAAO,GAAG,YAAY,CAAC;YACzB,CAAC;iBAAM,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;gBACjD,OAAO,GAAG,iBAAiB,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC9B,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,YAAY,KAAK,EAAE,CAAC;YACtC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAC5B,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;YACxB,OAAO,GAAG;gBACR,IAAI,EAAE,SAAS,CAAC,IAAI;gBACpB,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC;QACJ,CAAC;QAGD,IAAI,OAAO,KAAK,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;YACtC,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO;YACL,IAAI;YACJ,OAAO;YACP,IAAI;YACJ,IAAI,EAAE,IAAA,sCAAY,EAAC,IAAI,CAAC;YACxB,KAAK;YACL,OAAO;SACR,CAAC;IACJ,CAAC;IAKO,kBAAkB,CACxB,aAAkB,EAClB,SAAiB,EACjB,OAAe,EACf,IAAY;QAEZ,OAAO,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAChD,aAAa,CAAC,OAAO,EACrB,aAAa,CAAC,IAAI,EAClB,aAAa,CAAC,IAAI,EAClB;YACE,SAAS;YACT,OAAO;YACP,IAAI;YACJ,SAAS,EAAE,aAAa,CAAC,IAAI;YAC7B,YAAY,EAAE,aAAa,CAAC,OAAO;YACnC,KAAK,EAAE,aAAa,CAAC,KAAK;SAC3B,CACF,CAAC;IACJ,CAAC;IAKO,QAAQ,CACd,SAAkB,EAClB,OAAgB,EAChB,aAAkB,EAClB,OAAe;QAEf,MAAM,UAAU,GAAG;YACjB,OAAO;YACP,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;YACpC,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,UAAU,EAAE,aAAa;YACnD,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,OAAO,EAAE,aAAa,CAAC,OAAO;YAC9B,IAAI,EAAE,aAAa,CAAC,IAAI;SACzB,CAAC;QAEF,IAAI,aAAa,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YAE9B,IAAI,CAAC,aAAa,CAAC,KAAK,CACtB,IAAI,OAAO,mBAAmB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,EACxF,SAAS,YAAY,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EACzE,wBAAwB,CACzB,CAAC;QACJ,CAAC;aAAM,IAAI,aAAa,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;YAErC,IAAI,CAAC,aAAa,CAAC,IAAI,CACrB,IAAI,OAAO,mBAAmB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,EACxF,wBAAwB,CACzB,CAAC;QACJ,CAAC;aAAM,CAAC;YAEN,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,IAAI,OAAO,gBAAgB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,MAAM,aAAa,CAAC,OAAO,EAAE,EACrF,wBAAwB,CACzB,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,sBAAsB,CAAC,IAAY;QACzC,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,mBAAU,CAAC,YAAY;gBAC1B,OAAO,UAAU,CAAC;YACpB,KAAK,mBAAU,CAAC,SAAS;gBACvB,OAAO,YAAY,CAAC;YACtB,KAAK,mBAAU,CAAC,SAAS;gBACvB,OAAO,UAAU,CAAC;YACpB,KAAK,mBAAU,CAAC,WAAW;gBACzB,OAAO,QAAQ,CAAC;YAClB,KAAK,mBAAU,CAAC,QAAQ;gBACtB,OAAO,MAAM,CAAC;YAChB,KAAK,mBAAU,CAAC,oBAAoB;gBAClC,OAAO,QAAQ,CAAC;YAClB,KAAK,mBAAU,CAAC,iBAAiB;gBAC/B,OAAO,cAAc,CAAC;YACxB,KAAK,mBAAU,CAAC,qBAAqB;gBACnC,OAAO,SAAS,CAAC;YACnB,KAAK,mBAAU,CAAC,WAAW;gBACzB,OAAO,MAAM,CAAC;YAChB,KAAK,mBAAU,CAAC,mBAAmB;gBACjC,OAAO,SAAS,CAAC;YACnB,KAAK,mBAAU,CAAC,eAAe;gBAC7B,OAAO,MAAM,CAAC;YAChB;gBACE,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;oBAChB,OAAO,OAAO,CAAC;gBACjB,CAAC;qBAAM,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;oBACvB,OAAO,MAAM,CAAC;gBAChB,CAAC;qBAAM,CAAC;oBACN,OAAO,MAAM,CAAC;gBAChB,CAAC;QACL,CAAC;IACH,CAAC;CAGF,CAAA;AAvNY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,cAAK,GAAE;qCAGwC,wDAAyB;QACrC,8BAAa;GAHpC,sBAAsB,CAuNlC"}
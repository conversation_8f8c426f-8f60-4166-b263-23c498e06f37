import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  Req,
  UsePipes,
  ValidationPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery
} from '@nestjs/swagger';
import { Request } from 'express';

import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
import { LocationInfoResponseDto } from '../application/dto/responses/location-info.response.dto';
import { RiskAssessmentResponseDto } from '../application/dto/responses/risk-assessment.response.dto';
import { LocationStatsResponseDto } from '../application/dto/responses/location-stats.response.dto';

/**
 * IP地理位置控制器
 * 基于DDD架构提供IP地理位置查询、风险评估、用户位置统计等API接口
 * 支持新的门面服务和原有应用服务
 */
@ApiTags('IP地理位置')
@ApiBearerAuth('access-token')
@Controller('api/v1/ip-location')
@UsePipes(new ValidationPipe({
  transform: true,
  transformOptions: { enableImplicitConversion: true },
  whitelist: true,
  forbidNonWhitelisted: true
}))
export class IpLocationController {
  constructor(
    // 统一使用门面服务作为唯一依赖
    private readonly ipLocationFacadeService: IpLocationFacadeService
  ) {}



  /**
   * 查询IP地理位置信息 (DDD架构版本)
   */
  @Get('query')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '查询IP地理位置 (DDD架构)',
    description: '基于DDD架构的IP地理位置查询，性能更优，功能更强'
  })
  @ApiQuery({ name: 'ip', description: 'IP地址', example: '**************' })
  @ApiQuery({
    name: 'includeRisk',
    description: '是否包含风险评估',
    required: false,
    type: Boolean,
    example: false
  })
  @ApiResponse({ status: 200, description: '查询成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async queryIpLocationV2(@Query() query: IpQueryRequestDto) {
    const result = await this.ipLocationFacadeService.getLocationByIP(
      query.ip,
      query.includeRisk
    );

    if (!result.success) {
      // 门面服务返回的错误格式
      throw new Error((result as any).error || '查询失败');
    }

    // 直接返回门面服务的完整响应，包含所有字段
    return result;
  }

  /**
   * 登录风险检查
   */
  @Post('check-risk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '登录风险检查',
    description: '基于IP地址和用户历史进行登录风险评估'
  })
  @ApiResponse({ 
    status: 200, 
    description: '风险评估成功', 
    type: RiskAssessmentResponseDto 
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async checkLoginRisk(@Body() request: RiskCheckRequestDto) {
    const result = await this.ipLocationFacadeService.assessLoginRisk(
      request.userId,
      request.ipAddress,
      request.userAgent,
      request.sessionId
    );

    if (!result.success) {
      throw new Error((result as any).error || '风险评估失败');
    }

    // 直接返回门面服务的完整响应，包含requestId等元数据
    return result;
  }

  /**
   * 获取用户位置统计
   */
  @Get('user/:userId/stats')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '获取用户位置统计',
    description: '获取用户的常用登录地统计信息'
  })
  @ApiParam({ name: 'userId', description: '用户ID', type: Number })
  @ApiQuery({ 
    name: 'days', 
    description: '统计天数', 
    required: false, 
    type: Number,
    example: 30 
  })
  @ApiQuery({ 
    name: 'includeTrusted', 
    description: '是否包含可信位置', 
    required: false, 
    type: Boolean,
    example: true 
  })
  @ApiResponse({ 
    status: 200, 
    description: '查询成功', 
    type: LocationStatsResponseDto 
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getUserLocationStats(
    @Param('userId') userId: number,
    @Query('days') days: number = 30
  ) {
    const result = await this.ipLocationFacadeService.getUserLocationStats(userId, days);

    if (!result.success) {
      throw new Error((result as any).error || '获取统计失败');
    }

    // 直接返回门面服务的完整响应，包含requestId等元数据
    return result;
  }

  /**
   * 设置可信登录地
   */
  @Post('user/:userId/trust')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '设置可信登录地',
    description: '将指定位置设置为用户的可信登录地'
  })
  @ApiParam({ name: 'userId', description: '用户ID', type: Number })
  @ApiResponse({ status: 200, description: '设置成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async setTrustedLocation(
    @Param('userId') userId: number,
    @Body() request: TrustLocationRequestDto
  ) {
    const result = await this.ipLocationFacadeService.setTrustedLocation(
      userId,
      request.province,
      request.city,
      request.reason
    );

    if (!result.success) {
      throw new Error((result as any).error || '设置可信位置失败');
    }

    // 直接返回门面服务的完整响应，包含requestId等元数据
    return result;
  }

  /**
   * 获取当前请求的IP地理位置（便捷接口）
   */
  @Get('current')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '获取当前IP位置',
    description: '获取当前请求IP的地理位置信息'
  })
  @ApiResponse({ 
    status: 200, 
    description: '查询成功', 
    type: LocationInfoResponseDto 
  })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async getCurrentIpLocation(@Req() request: Request) {
    const clientIp = this.extractClientIP(request);

    const result = await this.ipLocationFacadeService.getLocationByIP(clientIp, false);

    if (!result.success) {
      throw new Error((result as any).error || '获取当前IP位置失败');
    }

    // 直接返回门面服务的完整响应，包含requestId等元数据
    return result;
  }

  /**
   * 健康检查接口
   */
  @Get('health')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: '健康检查',
    description: 'IP地理位置服务健康检查'
  })
  @ApiResponse({ status: 200, description: '服务正常' })
  async healthCheck(): Promise<{ status: string; timestamp: string; service: string }> {
    return {
      status: 'UP',
      timestamp: new Date().toISOString(),
      service: 'ip-location'
    };
  }

  /**
   * 提取客户端IP地址
   * @param request HTTP请求对象
   * @returns 客户端IP地址
   */
  private extractClientIP(request: Request): string {
    // 检查各种可能的IP头部
    const forwarded = request.headers['x-forwarded-for'] as string;
    const realIp = request.headers['x-real-ip'] as string;
    const cfConnectingIp = request.headers['cf-connecting-ip'] as string; // Cloudflare
    const xClientIp = request.headers['x-client-ip'] as string;
    const xClusterClientIp = request.headers['x-cluster-client-ip'] as string;

    const clientIp = (request.socket as any)?.remoteAddress ||
                     (request as any)?.ip;

    // 添加详细的IP提取日志
    console.log('🖥️ [Backend] IP地址提取详情:', {
      请求路径: request.url,
      请求方法: request.method,
      请求头IP信息: {
        'x-forwarded-for': forwarded,
        'x-real-ip': realIp,
        'cf-connecting-ip': cfConnectingIp,
        'x-client-ip': xClientIp,
        'x-cluster-client-ip': xClusterClientIp,
      },
      连接IP信息: {
        'socket.remoteAddress': (request.socket as any)?.remoteAddress,
        'request.ip': (request as any)?.ip,
        '最终连接IP': clientIp
      },
      时间戳: new Date().toISOString()
    });

    // 按优先级检查各种IP头部
    const ipSources = [
      forwarded?.split(',')[0]?.trim(),  // x-forwarded-for (第一个IP)
      cfConnectingIp,                    // Cloudflare
      realIp,                           // x-real-ip
      xClientIp,                        // x-client-ip
      xClusterClientIp,                 // x-cluster-client-ip
      clientIp                          // 直连IP
    ];

    for (const ip of ipSources) {
      if (ip) {
        const cleanIp = this.cleanAndValidateIP(ip);
        if (cleanIp) {
          console.log('✅ [Backend] IP地址提取成功:', {
            原始IP: ip,
            清理后IP: cleanIp,
            来源: this.getIpSourceName(ip, {
              forwarded: forwarded?.split(',')[0]?.trim(),
              cfConnectingIp,
              realIp,
              xClientIp,
              xClusterClientIp,
              clientIp
            }),
            时间戳: new Date().toISOString()
          });
          return cleanIp;
        }
      }
    }

    // 默认返回本地IPv4地址（便于测试）
    console.log('⚠️ [Backend] 使用默认IP地址:', {
      原因: '所有IP源都无效',
      默认IP: '127.0.0.1',
      时间戳: new Date().toISOString()
    });
    return '127.0.0.1';
  }

  /**
   * 清理和验证IP地址
   * @param ip 原始IP地址
   * @returns 清理后的IP地址或null
   */
  private cleanAndValidateIP(ip: string): string | null {
    if (!ip) return null;

    let cleanIp = ip.trim();

    // 移除IPv6映射的IPv4前缀
    cleanIp = cleanIp.replace('::ffff:', '');

    // 处理IPv6本地回环地址
    if (cleanIp === '::1') {
      // 在测试环境中，将IPv6本地回环转换为IPv4
      return '127.0.0.1';
    }

    // 验证IP地址格式
    if (this.isValidIP(cleanIp)) {
      return cleanIp;
    }

    return null;
  }

  /**
   * 验证IP地址格式
   * @param ip IP地址
   * @returns 是否为有效IP
   */
  private isValidIP(ip: string): boolean {
    // IPv4格式验证
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6格式验证（简化版）
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }

  /**
   * 获取IP来源名称
   * @param ip 当前IP
   * @param sources IP来源对象
   * @returns IP来源描述
   */
  private getIpSourceName(ip: string, sources: {
    forwarded?: string;
    cfConnectingIp?: string;
    realIp?: string;
    xClientIp?: string;
    xClusterClientIp?: string;
    clientIp?: string;
  }): string {
    if (ip === sources.forwarded) return 'x-forwarded-for';
    if (ip === sources.cfConnectingIp) return 'cf-connecting-ip (Cloudflare)';
    if (ip === sources.realIp) return 'x-real-ip';
    if (ip === sources.xClientIp) return 'x-client-ip';
    if (ip === sources.xClusterClientIp) return 'x-cluster-client-ip';
    if (ip === sources.clientIp) return 'connection.remoteAddress';
    return '未知来源';
  }
}

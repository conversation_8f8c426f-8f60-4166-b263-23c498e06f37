"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
const logger_service_1 = require("../../common/logger/logger.service");
const http_response_interface_1 = require("../http_response_result/http-response.interface");
let UnifiedExceptionFilter = class UnifiedExceptionFilter {
    httpResponseResultService;
    loggerService;
    constructor(httpResponseResultService, loggerService) {
        this.httpResponseResultService = httpResponseResultService;
        this.loggerService = loggerService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const requestId = this.generateRequestId();
        const traceId = request.traceId || this.generateTraceId();
        const path = request.url;
        const exceptionInfo = this.parseException(exception);
        this.logError(exception, request, exceptionInfo, traceId);
        const errorResponse = this.buildErrorResponse(exceptionInfo, requestId, traceId, path);
        const httpStatus = (0, http_response_interface_1.getHttpStatusCode)(exceptionInfo.code);
        response
            .status(httpStatus)
            .json(errorResponse);
    }
    parseException(exception) {
        let code = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = '服务器内部错误';
        let data = null;
        let details = {};
        let stack;
        if (exception instanceof common_1.HttpException) {
            code = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'object' && exceptionResponse !== null) {
                const exceptionObj = exceptionResponse;
                if (exceptionObj.msg) {
                    message = exceptionObj.msg;
                }
                else if (exceptionObj.message) {
                    message = exceptionObj.message;
                }
                else {
                    message = exception.message;
                }
                if (exceptionObj.code) {
                    code = exceptionObj.code;
                }
                if (exceptionObj.data !== undefined) {
                    data = exceptionObj.data;
                }
                details = exceptionObj;
            }
            else if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
            }
            else {
                message = exception.message;
            }
        }
        else if (exception instanceof Error) {
            message = exception.message;
            stack = exception.stack;
            details = {
                name: exception.name,
                message: exception.message
            };
        }
        if (message === '服务器内部错误' || !message) {
            message = this.getDefaultErrorMessage(code);
        }
        return {
            code,
            message,
            data,
            type: (0, http_response_interface_1.getErrorType)(code),
            stack,
            details
        };
    }
    buildErrorResponse(exceptionInfo, requestId, traceId, path) {
        return this.httpResponseResultService.unifiedError(exceptionInfo.message, exceptionInfo.data, exceptionInfo.code, {
            requestId,
            traceId,
            path,
            errorType: exceptionInfo.type,
            errorDetails: exceptionInfo.details,
            stack: exceptionInfo.stack
        });
    }
    logError(exception, request, exceptionInfo, traceId) {
        const logContext = {
            traceId,
            method: request.method,
            url: request.url,
            userAgent: request.get('User-Agent'),
            ip: request.ip || request.connection?.remoteAddress,
            code: exceptionInfo.code,
            message: exceptionInfo.message,
            type: exceptionInfo.type
        };
        if (exceptionInfo.code >= 500) {
            this.loggerService.error(`[${traceId}] Server Error: ${request.method} ${request.url} - ${exceptionInfo.message}`, exception instanceof Error ? exception.stack : JSON.stringify(logContext), 'UnifiedExceptionFilter');
        }
        else if (exceptionInfo.code >= 400) {
            this.loggerService.warn(`[${traceId}] Client Error: ${request.method} ${request.url} - ${exceptionInfo.message}`, 'UnifiedExceptionFilter');
        }
        else {
            this.loggerService.info(`[${traceId}] Exception: ${request.method} ${request.url} - ${exceptionInfo.message}`, 'UnifiedExceptionFilter');
        }
    }
    getDefaultErrorMessage(code) {
        switch (code) {
            case common_1.HttpStatus.UNAUTHORIZED:
                return '请先登录后再访问';
            case common_1.HttpStatus.FORBIDDEN:
                return '您没有权限执行此操作';
            case common_1.HttpStatus.NOT_FOUND:
                return '请求的资源不存在';
            case common_1.HttpStatus.BAD_REQUEST:
                return '请求参数错误';
            case common_1.HttpStatus.CONFLICT:
                return '资源冲突';
            case common_1.HttpStatus.UNPROCESSABLE_ENTITY:
                return '数据验证失败';
            case common_1.HttpStatus.TOO_MANY_REQUESTS:
                return '请求过于频繁，请稍后重试';
            case common_1.HttpStatus.INTERNAL_SERVER_ERROR:
                return '服务器内部错误';
            case common_1.HttpStatus.BAD_GATEWAY:
                return '网关错误';
            case common_1.HttpStatus.SERVICE_UNAVAILABLE:
                return '服务暂时不可用';
            case common_1.HttpStatus.GATEWAY_TIMEOUT:
                return '网关超时';
            default:
                if (code >= 500) {
                    return '服务器错误';
                }
                else if (code >= 400) {
                    return '请求错误';
                }
                else {
                    return '未知错误';
                }
        }
    }
    generateRequestId() {
        return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
};
exports.UnifiedExceptionFilter = UnifiedExceptionFilter;
exports.UnifiedExceptionFilter = UnifiedExceptionFilter = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [http_response_result_service_1.HttpResponseResultService,
        logger_service_1.LoggerService])
], UnifiedExceptionFilter);
//# sourceMappingURL=unified-exception.filter.js.map
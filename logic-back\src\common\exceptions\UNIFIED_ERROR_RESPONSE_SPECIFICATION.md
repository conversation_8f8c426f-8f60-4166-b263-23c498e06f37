# 统一错误响应规范

## 📋 概述

本文档定义了整个应用的统一错误响应格式和处理规范，确保前后端对接的一致性和用户体验的统一性。

## 🎯 设计原则

1. **一致性**：所有错误响应使用相同的格式
2. **可读性**：错误信息对开发者和用户都友好
3. **可追踪性**：包含足够的信息用于问题定位
4. **国际化**：支持多语言错误消息
5. **向后兼容**：不破坏现有的API契约

## 📊 统一响应格式

### 基础响应结构
```typescript
interface UnifiedResponse<T = any> {
  /** 业务状态码 */
  code: number;
  
  /** 响应消息 */
  msg: string;
  
  /** 响应数据 */
  data: T | null;
  
  /** 成功标识 */
  success: boolean;
  
  /** 时间戳 */
  timestamp: string;
  
  /** 追踪信息（可选） */
  trace?: {
    /** 请求ID */
    requestId?: string;
    /** 追踪ID */
    traceId?: string;
    /** 请求路径 */
    path?: string;
  };
  
  /** 错误详情（仅错误响应） */
  error?: {
    /** 错误类型 */
    type: string;
    /** 错误详情 */
    details?: any;
    /** 错误堆栈（仅开发环境） */
    stack?: string;
  };
}
```

### 成功响应示例
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": { "id": 123, "name": "用户名" },
  "success": true,
  "timestamp": "2025-01-15T10:30:00.000Z",
  "trace": {
    "requestId": "req-1705312200000-abc123",
    "path": "/api/v1/users/123"
  }
}
```

### 错误响应示例
```json
{
  "code": 400,
  "msg": "请求参数错误",
  "data": null,
  "success": false,
  "timestamp": "2025-01-15T10:30:00.000Z",
  "trace": {
    "requestId": "req-1705312200000-abc123",
    "traceId": "trace-1705312200000-xyz789",
    "path": "/api/v1/users"
  },
  "error": {
    "type": "ValidationError",
    "details": {
      "field": "email",
      "message": "邮箱格式不正确",
      "value": "invalid-email"
    }
  }
}
```

## 🔢 状态码规范

### HTTP状态码 vs 业务状态码

#### HTTP状态码（响应头）
- **200**: 请求成功
- **400**: 客户端错误
- **401**: 未授权
- **403**: 权限不足
- **404**: 资源不存在
- **500**: 服务器错误

#### 业务状态码（响应体code字段）
```typescript
// 成功状态码
export const SUCCESS_CODES = {
  OK: 200,                    // 操作成功
  CREATED: 201,              // 创建成功
  ACCEPTED: 202,             // 已接受处理
  PARTIAL_SUCCESS: 206,      // 部分成功
} as const;

// 客户端错误状态码
export const CLIENT_ERROR_CODES = {
  BAD_REQUEST: 400,          // 请求参数错误
  UNAUTHORIZED: 401,         // 未授权
  FORBIDDEN: 403,            // 权限不足
  NOT_FOUND: 404,            // 资源不存在
  METHOD_NOT_ALLOWED: 405,   // 方法不允许
  CONFLICT: 409,             // 资源冲突
  VALIDATION_ERROR: 422,     // 数据验证错误
  TOO_MANY_REQUESTS: 429,    // 请求过于频繁
} as const;

// 服务器错误状态码
export const SERVER_ERROR_CODES = {
  INTERNAL_ERROR: 500,       // 服务器内部错误
  NOT_IMPLEMENTED: 501,      // 功能未实现
  BAD_GATEWAY: 502,          // 网关错误
  SERVICE_UNAVAILABLE: 503,  // 服务不可用
  GATEWAY_TIMEOUT: 504,      // 网关超时
} as const;

// 业务错误状态码
export const BUSINESS_ERROR_CODES = {
  // 用户相关
  USER_NOT_FOUND: 1001,
  USER_ALREADY_EXISTS: 1002,
  USER_DISABLED: 1003,
  
  // 认证相关
  TOKEN_EXPIRED: 2001,
  TOKEN_INVALID: 2002,
  LOGIN_FAILED: 2003,
  
  // 权限相关
  PERMISSION_DENIED: 3001,
  ROLE_NOT_FOUND: 3002,
  
  // 业务逻辑相关
  INSUFFICIENT_BALANCE: 4001,
  OPERATION_NOT_ALLOWED: 4002,
  RESOURCE_LOCKED: 4003,
  
  // 系统相关
  SYSTEM_MAINTENANCE: 5001,
  RATE_LIMIT_EXCEEDED: 5002,
  EXTERNAL_SERVICE_ERROR: 5003,
} as const;
```

## 🏗️ 错误分类体系

### 1. 系统错误（System Errors）
- **网络错误**：连接超时、网络不可达
- **服务错误**：数据库连接失败、第三方服务异常
- **配置错误**：配置文件错误、环境变量缺失

### 2. 业务错误（Business Errors）
- **验证错误**：参数格式错误、必填字段缺失
- **逻辑错误**：业务规则违反、状态不匹配
- **权限错误**：无权限访问、角色不匹配

### 3. 用户错误（User Errors）
- **认证错误**：登录失败、Token过期
- **授权错误**：权限不足、资源不可访问
- **操作错误**：重复操作、非法操作

## 📝 错误消息规范

### 消息格式
```typescript
interface ErrorMessage {
  /** 用户友好的错误消息 */
  userMessage: string;
  
  /** 开发者错误消息 */
  developerMessage: string;
  
  /** 错误代码 */
  errorCode: string;
  
  /** 帮助链接（可选） */
  moreInfo?: string;
}
```

### 消息示例
```typescript
const ERROR_MESSAGES = {
  VALIDATION_ERROR: {
    userMessage: '输入信息有误，请检查后重试',
    developerMessage: 'Request validation failed: email format is invalid',
    errorCode: 'VALIDATION_ERROR',
    moreInfo: 'https://docs.example.com/errors/validation'
  },
  
  INSUFFICIENT_BALANCE: {
    userMessage: '余额不足，请先充值',
    developerMessage: 'User balance is insufficient for this operation',
    errorCode: 'INSUFFICIENT_BALANCE',
    moreInfo: 'https://docs.example.com/errors/balance'
  }
};
```

## 🔧 实现指南

### 1. 统一异常类
```typescript
export class UnifiedException extends Error {
  constructor(
    public readonly code: number,
    public readonly userMessage: string,
    public readonly developerMessage: string,
    public readonly errorType: string,
    public readonly details?: any
  ) {
    super(developerMessage);
    this.name = 'UnifiedException';
  }
}
```

### 2. 统一异常过滤器
```typescript
@Catch()
export class UnifiedExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    
    const errorResponse = this.buildErrorResponse(exception, request);
    
    response
      .status(this.getHttpStatus(errorResponse.code))
      .json(errorResponse);
  }
  
  private buildErrorResponse(exception: unknown, request: Request): UnifiedResponse {
    // 构建统一错误响应
  }
}
```

### 3. 响应构建器
```typescript
export class ResponseBuilder {
  static success<T>(data: T, message = '操作成功'): UnifiedResponse<T> {
    return {
      code: 200,
      msg: message,
      data,
      success: true,
      timestamp: new Date().toISOString()
    };
  }
  
  static error(
    code: number,
    message: string,
    details?: any
  ): UnifiedResponse<null> {
    return {
      code,
      msg: message,
      data: null,
      success: false,
      timestamp: new Date().toISOString(),
      error: {
        type: this.getErrorType(code),
        details
      }
    };
  }
}
```

## 🎯 最佳实践

### 1. 错误处理原则
- **快速失败**：尽早发现和处理错误
- **优雅降级**：提供备选方案
- **详细日志**：记录足够的错误信息
- **用户友好**：提供清晰的错误提示

### 2. 开发规范
- 使用统一的异常类
- 记录详细的错误日志
- 提供有意义的错误消息
- 包含足够的上下文信息

### 3. 测试要求
- 覆盖所有错误场景
- 验证错误响应格式
- 测试错误消息的准确性
- 检查日志记录的完整性

---

**总结**: 通过统一的错误响应规范，我们可以提供一致的用户体验，简化前端错误处理，并提高系统的可维护性和可观测性。🐱

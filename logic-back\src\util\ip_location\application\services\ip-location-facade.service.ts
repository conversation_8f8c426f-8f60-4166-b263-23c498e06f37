import { Injectable } from '@nestjs/common';
import { LoggerService } from '../../../../common/logger/logger.service';

// 应用服务 - 新增导入
import { IpLocationApplicationService } from './ip-location-application.service';

// 请求和响应DTO
import { IpQueryRequestDto } from '../dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../dto/requests/trust-location.request.dto';

// 领域服务 - 仅用于测试功能
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';

// 全局HTTP响应服务
import { HttpResponseResultService } from '../../../../web/http_response_result/http_response_result.service';
import { EnhancedHttpResponse, UnifiedResponse } from '../../../../web/http_response_result/http-response.interface';
// 统一请求ID工具
import { RequestIdUtils, BusinessModule } from '../../../../common/middleware/request-id.middleware';

/**
 * IP地理位置门面服务
 * 基于DDD架构提供统一的业务接口
 * 作为系统对外的统一入口，内部委托给应用服务处理复杂业务逻辑
 */
@Injectable()
export class IpLocationFacadeService {
  constructor(
    // 应用服务 - 处理所有业务逻辑
    private readonly applicationService: IpLocationApplicationService,

    // 领域服务 - 仅用于测试功能
    private readonly domainService: IpLocationDomainService,

    // 基础服务
    private readonly logger: LoggerService,

    // 全局HTTP响应服务 - 统一响应格式
    private readonly httpResponseService: HttpResponseResultService,
  ) {}

  /**
   * 查询IP地理位置信息
   * @param ip IP地址字符串
   * @param includeRisk 是否包含风险评估
   * @returns 统一格式的位置信息响应
   */
  async getLocationByIP(ip: string, includeRisk: boolean = false): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理业务逻辑
      const request: IpQueryRequestDto = { ip, includeRisk };
      const result = await this.applicationService.queryIpLocation(request);

      // 使用全局响应服务的增强方法
      return this.httpResponseService.enhancedSuccess(
        result,
        '查询成功',
        200,
        {
          executionTime: Date.now() - startTime,
          fromCache: false, // TODO: 可以从缓存服务获取实际状态
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0'
          },
          logContext: {
            module: 'IpLocation',
            operation: 'queryLocation',
            requestData: { ip, includeRisk }
          }
        }
      );
    } catch (error) {
      this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');

      return this.httpResponseService.enhancedError(
        error.message || '查询失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0'
          },
          logContext: {
            module: 'IpLocation',
            operation: 'queryLocation',
            requestData: { ip, includeRisk },
            error: error
          }
        }
      );
    }
  }

  /**
   * 评估登录风险
   * @param userId 用户ID
   * @param ip IP地址
   * @param userAgent 用户代理
   * @param sessionId 会话ID
   * @returns 统一格式的风险评估响应
   */
  async assessLoginRisk(
    userId: number,
    ip: string,
    userAgent?: string,
    sessionId?: string
  ): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理复杂的风险评估逻辑
      const request: RiskCheckRequestDto = {
        userId,
        ipAddress: ip,
        userAgent,
        sessionId
      };

      const result = await this.applicationService.checkLoginRisk(request);

      return this.httpResponseService.enhancedSuccess(
        result,
        '风险评估完成',
        200,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString()
          },
          logContext: {
            module: 'IpLocation',
            operation: 'assessLoginRisk',
            userId: userId.toString(),
            requestData: { userId, ip, userAgent, sessionId }
          }
        }
      );
    } catch (error) {
      this.logger.error(`登录风险评估失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');

      return this.httpResponseService.enhancedError(
        error.message || '风险评估失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString()
          }
        }
      );
    }
  }

  /**
   * 获取用户位置统计
   * @param userId 用户ID
   * @param days 统计天数
   * @returns 统一格式的位置统计响应
   */
  async getUserLocationStats(
    userId: number,
    days: number = 30
  ): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理统计逻辑
      const result = await this.applicationService.getUserLocationStatistics(userId, days);

      return this.httpResponseService.enhancedSuccess(
        result,
        '统计查询成功',
        200,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString(),
            queryDays: days.toString()
          }
        }
      );
    } catch (error) {
      this.logger.error(`用户位置统计失败: ${userId}`, error, 'IpLocationFacadeService');

      return this.httpResponseService.enhancedError(
        error.message || '统计查询失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString()
          }
        }
      );
    }
  }



  /**
   * 设置可信位置
   * @param userId 用户ID
   * @param province 省份
   * @param city 城市
   * @param reason 设置原因
   * @returns 统一格式的设置结果响应
   */
  async setTrustedLocation(
    userId: number,
    province: string,
    city: string,
    reason: string = '用户主动设置'
  ): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      // 委托给应用服务处理
      const request: TrustLocationRequestDto = {
        province,
        city,
        reason
      };

      await this.applicationService.setTrustedLocation(userId, request);

      return this.httpResponseService.enhancedSuccess(
        { userId, province, city, reason },
        '可信位置设置成功',
        200,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString(),
            operation: 'setTrustedLocation'
          }
        }
      );
    } catch (error) {
      this.logger.error(`设置可信位置失败: ${userId}-${province}-${city}`, error, 'IpLocationFacadeService');

      return this.httpResponseService.enhancedError(
        error.message || '设置失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString(),
            operation: 'setTrustedLocation'
          }
        }
      );
    }
  }

  /**
   * 更新用户常用位置
   * @param userId 用户ID
   * @param ip IP地址
   * @returns 统一格式的更新结果响应
   */
  async updateUserCommonLocation(
    userId: number,
    ip: string
  ): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      // 先获取IP位置信息
      const request: IpQueryRequestDto = { ip, includeRisk: false };
      const locationInfo = await this.applicationService.queryIpLocation(request);

      // 注意：这里需要转换类型，实际项目中应该统一DTO类型
      // 暂时跳过更新，只返回位置信息
      // await this.applicationService.updateUserCommonLocation(userId, locationInfo);

      return this.httpResponseService.enhancedSuccess(
        { userId, ip, location: locationInfo },
        '常用位置更新成功',
        200,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString(),
            operation: 'updateUserCommonLocation'
          }
        }
      );
    } catch (error) {
      this.logger.error(`更新常用位置失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');

      return this.httpResponseService.enhancedError(
        error.message || '更新失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            userId: userId.toString(),
            operation: 'updateUserCommonLocation'
          }
        }
      );
    }
  }

  /**
   * 测试IP解析功能
   * @param testIp 测试IP
   * @returns 统一格式的测试结果响应
   */
  async testIpResolution(testIp: string = '*******'): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      const result = await this.domainService.testResolution(testIp);

      return this.httpResponseService.enhancedSuccess(
        result,
        result.success ? '测试成功' : '测试失败',
        200,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            testIp,
            operation: 'testIpResolution'
          }
        }
      );
    } catch (error) {
      this.logger.error(`IP解析测试失败: ${testIp}`, error, 'IpLocationFacadeService');

      return this.httpResponseService.enhancedError(
        error.message || '测试失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          includeTimestamp: true,
          meta: {
            requestId: this.generateRequestId(),
            version: 'v2.0.0',
            testIp,
            operation: 'testIpResolution'
          }
        }
      );
    }
  }

  // ==================== 私有辅助方法 ====================

  // ==================== 新增：统一响应方法示例 ====================

  /**
   * 查询IP地理位置信息（统一响应版本）
   * 演示如何使用新的统一响应格式，同时保持向后兼容
   * @param ip IP地址字符串
   * @param includeRisk 是否包含风险评估
   * @returns 统一格式的位置信息响应
   */
  async getLocationByIPUnified(ip: string, includeRisk: boolean = false): Promise<UnifiedResponse> {
    const startTime = Date.now();
    const requestId = this.generateRequestId();

    try {
      // 委托给应用服务处理业务逻辑
      const request: IpQueryRequestDto = { ip, includeRisk };
      const result = await this.applicationService.queryIpLocation(request);

      // 使用新的统一响应方法
      return this.httpResponseService.unifiedSuccess(
        result,
        '查询成功',
        200,
        {
          requestId,
          traceId: `trace-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          path: '/api/v1/ip-location/query',
          executionTime: Date.now() - startTime,
          fromCache: false // TODO: 可以从缓存服务获取实际状态
        }
      );
    } catch (error) {
      this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');

      // 使用统一错误响应
      return this.httpResponseService.unifiedError(
        error.message || '查询失败',
        null,
        500,
        {
          requestId,
          traceId: `trace-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          path: '/api/v1/ip-location/query',
          errorDetails: { ip, includeRisk },
          stack: error.stack,
          executionTime: Date.now() - startTime
        }
      );
    }
  }

  /**
   * 业务错误示例：用户权限不足
   * 演示如何使用业务错误码
   */
  async checkUserPermission(userId: number): Promise<UnifiedResponse> {
    const requestId = this.generateRequestId();

    // 模拟权限检查失败
    if (userId < 1000) {
      return this.httpResponseService.businessError(
        3001, // PERMISSION_DENIED
        '您没有权限访问IP地理位置服务',
        { userId, requiredRole: 'admin' },
        {
          requestId,
          path: '/api/v1/ip-location/permission-check',
          errorDetails: { reason: 'User ID too low', minRequiredId: 1000 }
        }
      );
    }

    return this.httpResponseService.unifiedSuccess(
      { userId, hasPermission: true },
      '权限验证通过',
      200,
      { requestId, path: '/api/v1/ip-location/permission-check' }
    );
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成请求ID（使用IP地理位置模块专用前缀）
   * @returns 唯一的请求ID
   */
  private generateRequestId(): string {
    // 使用IP地理位置模块专用的请求ID格式
    return RequestIdUtils.generateBusinessRequestId(BusinessModule.IP_LOCATION);
  }

  /**
   * 获取缓存状态（TODO: 实际实现需要从缓存服务获取）
   * @param _key 缓存键（暂未使用）
   * @returns 是否来自缓存
   */
  private async getCacheStatus(_key: string): Promise<boolean> {
    // TODO: 实际实现需要检查Redis缓存
    // 这里暂时返回false，后续可以集成实际的缓存检查逻辑
    return false;
  }

  /**
   * 记录性能指标（可选的性能监控）
   * @param operation 操作名称
   * @param executionTime 执行时间
   * @param success 是否成功
   */
  private logPerformanceMetrics(operation: string, executionTime: number, success: boolean): void {
    // TODO: 可以集成到监控系统中
    if (executionTime > 1000) { // 超过1秒的操作记录警告
      this.logger.warn(
        `慢操作检测: ${operation} 耗时 ${executionTime}ms - 成功: ${success}`,
        'IpLocationFacadeService'
      );
    }
  }


}

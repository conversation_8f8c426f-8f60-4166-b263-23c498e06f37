import { Global, Module } from '@nestjs/common';
import { HttpResponseResultService } from './http_response_result.service';
import { HttpResponseResultController } from './http_response_result.controller';
import { LoggerService } from '../../common/logger/logger.service';

@Global()
@Module({
  controllers: [HttpResponseResultController],
  providers: [
    HttpResponseResultService,
    LoggerService, // 添加LoggerService依赖
  ],
  exports: [HttpResponseResultService],
})
export class HttpResponseResultModule {}

import { Global, Module } from '@nestjs/common';
import { HttpResponseResultService } from './http_response_result.service';
import { HttpResponseResultController } from './http_response_result.controller';

@Global()
@Module({
  controllers: [HttpResponseResultController],
  providers: [
    HttpResponseResultService,
    // LoggerService 现在是全局模块，不需要在这里重复注册
  ],
  exports: [HttpResponseResultService],
})
export class HttpResponseResultModule {}

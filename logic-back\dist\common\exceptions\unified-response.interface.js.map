{"version": 3, "file": "unified-response.interface.js", "sourceRoot": "", "sources": ["../../../src/common/exceptions/unified-response.interface.ts"], "names": [], "mappings": ";;;AAiRA,8CAEC;AAKD,oCAYC;AAzPY,QAAA,aAAa,GAAG;IAC3B,EAAE,EAAE,GAAG;IACP,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,eAAe,EAAE,GAAG;CACZ,CAAC;AAKE,QAAA,kBAAkB,GAAG;IAChC,WAAW,EAAE,GAAG;IAChB,YAAY,EAAE,GAAG;IACjB,SAAS,EAAE,GAAG;IACd,SAAS,EAAE,GAAG;IACd,kBAAkB,EAAE,GAAG;IACvB,QAAQ,EAAE,GAAG;IACb,gBAAgB,EAAE,GAAG;IACrB,iBAAiB,EAAE,GAAG;CACd,CAAC;AAKE,QAAA,kBAAkB,GAAG;IAChC,cAAc,EAAE,GAAG;IACnB,eAAe,EAAE,GAAG;IACpB,WAAW,EAAE,GAAG;IAChB,mBAAmB,EAAE,GAAG;IACxB,eAAe,EAAE,GAAG;CACZ,CAAC;AAKE,QAAA,oBAAoB,GAAG;IAElC,cAAc,EAAE,IAAI;IACpB,mBAAmB,EAAE,IAAI;IACzB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IAGjB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,IAAI;IAClB,kBAAkB,EAAE,IAAI;IACxB,cAAc,EAAE,IAAI;IAGpB,iBAAiB,EAAE,IAAI;IACvB,cAAc,EAAE,IAAI;IACpB,uBAAuB,EAAE,IAAI;IAG7B,oBAAoB,EAAE,IAAI;IAC1B,qBAAqB,EAAE,IAAI;IAC3B,eAAe,EAAE,IAAI;IACrB,mBAAmB,EAAE,IAAI;IACzB,gBAAgB,EAAE,IAAI;IAGtB,kBAAkB,EAAE,IAAI;IACxB,mBAAmB,EAAE,IAAI;IACzB,sBAAsB,EAAE,IAAI;IAC5B,cAAc,EAAE,IAAI;IACpB,WAAW,EAAE,IAAI;CACT,CAAC;AAKX,IAAY,SASX;AATD,WAAY,SAAS;IACnB,yCAA4B,CAAA;IAC5B,6CAAgC,CAAA;IAChC,iDAAoC,CAAA;IACpC,yDAA4C,CAAA;IAC5C,uDAA0C,CAAA;IAC1C,2CAA8B,CAAA;IAC9B,6CAAgC,CAAA;IAChC,4DAA+C,CAAA;AACjD,CAAC,EATW,SAAS,yBAAT,SAAS,QASpB;AAsBY,QAAA,cAAc,GAAiC;IAE1D,gBAAgB,EAAE;QAChB,WAAW,EAAE,eAAe;QAC5B,gBAAgB,EAAE,2BAA2B;QAC7C,SAAS,EAAE,kBAAkB;KAC9B;IAGD,YAAY,EAAE;QACZ,WAAW,EAAE,UAAU;QACvB,gBAAgB,EAAE,yBAAyB;QAC3C,SAAS,EAAE,cAAc;KAC1B;IAED,aAAa,EAAE;QACb,WAAW,EAAE,aAAa;QAC1B,gBAAgB,EAAE,0BAA0B;QAC5C,SAAS,EAAE,eAAe;KAC3B;IAGD,SAAS,EAAE;QACT,WAAW,EAAE,YAAY;QACzB,gBAAgB,EAAE,0BAA0B;QAC5C,SAAS,EAAE,WAAW;KACvB;IAGD,SAAS,EAAE;QACT,WAAW,EAAE,UAAU;QACvB,gBAAgB,EAAE,8BAA8B;QAChD,SAAS,EAAE,WAAW;KACvB;IAGD,oBAAoB,EAAE;QACpB,WAAW,EAAE,WAAW;QACxB,gBAAgB,EAAE,iDAAiD;QACnE,SAAS,EAAE,sBAAsB;KAClC;IAED,mBAAmB,EAAE;QACnB,WAAW,EAAE,QAAQ;QACrB,gBAAgB,EAAE,8BAA8B;QAChD,SAAS,EAAE,qBAAqB;KACjC;IAGD,cAAc,EAAE;QACd,WAAW,EAAE,eAAe;QAC5B,gBAAgB,EAAE,gCAAgC;QAClD,SAAS,EAAE,gBAAgB;KAC5B;IAED,mBAAmB,EAAE;QACnB,WAAW,EAAE,eAAe;QAC5B,gBAAgB,EAAE,oCAAoC;QACtD,SAAS,EAAE,qBAAqB;KACjC;IAED,mBAAmB,EAAE;QACnB,WAAW,EAAE,cAAc;QAC3B,gBAAgB,EAAE,qBAAqB;QACvC,SAAS,EAAE,qBAAqB;KACjC;CACF,CAAC;AAKW,QAAA,mBAAmB,GAA2B;IAEzD,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IAGR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IAGR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IACR,GAAG,EAAE,GAAG;IAGR,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IAET,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IAET,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IAET,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IAET,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;IACT,IAAI,EAAE,GAAG;CACV,CAAC;AAKF,SAAgB,iBAAiB,CAAC,YAAoB;IACpD,OAAO,2BAAmB,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;AAClD,CAAC;AAKD,SAAgB,YAAY,CAAC,IAAY;IACvC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,cAAc,CAAC;IACjE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,oBAAoB,CAAC;IACvE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,mBAAmB,CAAC;IACtE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,cAAc,CAAC;IACjE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,YAAY,CAAC;IAC/D,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC,gBAAgB,CAAC;IACpE,IAAI,IAAI,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC,oBAAoB,CAAC;IACxD,IAAI,IAAI,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC,mBAAmB,CAAC;IACvD,IAAI,IAAI,IAAI,GAAG;QAAE,OAAO,SAAS,CAAC,YAAY,CAAC;IAE/C,OAAO,SAAS,CAAC,YAAY,CAAC;AAChC,CAAC"}
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 请求ID中间件
 * 为每个请求生成唯一的requestId和traceId，支持业务模块前缀
 */
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 根据请求路径推断业务模块，生成带业务前缀的请求ID
    const businessModule = RequestIdUtils.inferBusinessModule(req.url);
    const requestId = RequestIdUtils.generateBusinessRequestId(businessModule);
    const traceId = this.generateTraceId();

    // 将ID附加到请求对象上
    (req as any).requestId = requestId;
    (req as any).traceId = traceId;
    (req as any).businessModule = businessModule;

    // 将ID添加到响应头中（便于调试）
    res.setHeader('X-Request-ID', requestId);
    res.setHeader('X-Trace-ID', traceId);
    res.setHeader('X-Business-Module', businessModule);

    next();
  }

  /**
   * 生成统一格式的追踪ID
   */
  private generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * 业务模块前缀枚举
 */
export enum BusinessModule {
  IP_LOCATION = 'ip-loc',
  PAYMENT = 'payment',
  USER = 'user',
  AUTH = 'auth',
  COURSE = 'course',
  TASK = 'task',
  PACKAGE = 'package',
  SCRATCH = 'scratch',
  TPS = 'tps',
  GENERAL = 'req'  // 通用/未知模块
}

/**
 * 请求ID工具类
 * 提供统一的ID生成方法，支持业务模块前缀
 */
export class RequestIdUtils {
  /**
   * 从请求对象中获取requestId
   */
  static getRequestId(req: Request): string {
    return (req as any).requestId || RequestIdUtils.generateRequestId();
  }

  /**
   * 从请求对象中获取traceId
   */
  static getTraceId(req: Request): string {
    return (req as any).traceId || RequestIdUtils.generateTraceId();
  }

  /**
   * 从请求对象中获取业务模块前缀的requestId
   * 如果请求中没有，则根据路径自动推断业务模块
   */
  static getBusinessRequestId(req: Request, fallbackModule?: BusinessModule): string {
    const existingId = (req as any).requestId;
    if (existingId) {
      return existingId;
    }

    // 根据请求路径自动推断业务模块
    const businessModule = fallbackModule || RequestIdUtils.inferBusinessModule(req.url);
    return RequestIdUtils.generateBusinessRequestId(businessModule);
  }

  /**
   * 生成通用请求ID
   */
  static generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 生成追踪ID
   */
  static generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 为业务模块生成带前缀的请求ID
   */
  static generateBusinessRequestId(module: BusinessModule): string {
    return `${module}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 根据请求路径推断业务模块
   */
  static inferBusinessModule(path: string): BusinessModule {
    if (!path) return BusinessModule.GENERAL;

    // 移除查询参数
    const cleanPath = path.split('?')[0].toLowerCase();

    // 根据路径模式匹配业务模块
    if (cleanPath.includes('/ip-location')) return BusinessModule.IP_LOCATION;
    if (cleanPath.includes('/payment')) return BusinessModule.PAYMENT;
    if (cleanPath.includes('/user')) return BusinessModule.USER;
    if (cleanPath.includes('/auth') || cleanPath.includes('/login')) return BusinessModule.AUTH;
    if (cleanPath.includes('/course')) return BusinessModule.COURSE;
    if (cleanPath.includes('/task')) return BusinessModule.TASK;
    if (cleanPath.includes('/package')) return BusinessModule.PACKAGE;
    if (cleanPath.includes('/scratch')) return BusinessModule.SCRATCH;
    if (cleanPath.includes('/tps')) return BusinessModule.TPS;

    return BusinessModule.GENERAL;
  }

  /**
   * 检查requestId是否属于指定的业务模块
   */
  static isFromModule(requestId: string, module: BusinessModule): boolean {
    return requestId.startsWith(`${module}-`);
  }

  /**
   * 从requestId中提取业务模块前缀
   */
  static extractModulePrefix(requestId: string): string {
    const parts = requestId.split('-');
    return parts.length > 0 ? parts[0] : 'unknown';
  }
}

import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

/**
 * 请求ID中间件
 * 为每个请求生成唯一的requestId和traceId，确保整个请求生命周期中ID的一致性
 */
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 生成统一的请求ID和追踪ID
    const requestId = this.generateRequestId();
    const traceId = this.generateTraceId();
    
    // 将ID附加到请求对象上
    (req as any).requestId = requestId;
    (req as any).traceId = traceId;
    
    // 将ID添加到响应头中（便于调试）
    res.setHeader('X-Request-ID', requestId);
    res.setHeader('X-Trace-ID', traceId);
    
    next();
  }

  /**
   * 生成统一格式的请求ID
   */
  private generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 生成统一格式的追踪ID
   */
  private generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * 请求ID工具类
 * 提供统一的ID生成方法
 */
export class RequestIdUtils {
  /**
   * 从请求对象中获取requestId
   */
  static getRequestId(req: Request): string {
    return (req as any).requestId || RequestIdUtils.generateRequestId();
  }

  /**
   * 从请求对象中获取traceId
   */
  static getTraceId(req: Request): string {
    return (req as any).traceId || RequestIdUtils.generateTraceId();
  }

  /**
   * 生成请求ID
   */
  static generateRequestId(): string {
    return `req-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 生成追踪ID
   */
  static generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 为业务模块生成带前缀的请求ID
   */
  static generateBusinessRequestId(prefix: string): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}

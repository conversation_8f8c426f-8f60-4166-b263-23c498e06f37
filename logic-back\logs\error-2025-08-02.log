2025-08-02 10:48:28.801 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 10:48:28.944 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.944 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.946 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.947 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.947 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.948 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.949 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.950 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.950 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.951 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.952 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.952 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.953 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.954 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.955 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.956 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.959 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.959 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.960 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 10:48:28.960 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:17:38.538 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/health - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:17:38.543 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/health","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-08-02T03:17:38.539Z"}
2025-08-02 11:23:20.999 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.000 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.000Z"}
2025-08-02 11:23:21.137 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.141 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.138Z"}
2025-08-02 11:23:21.188 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.189 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.189Z"}
2025-08-02 11:23:21.202 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=5 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.203 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/packages?userId=5","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.202Z"}
2025-08-02 11:23:21.213 [ERROR] [GlobalExceptionFilter] GET /api/web/announcement/publishedIdsByTarget - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.214 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/publishedIdsByTarget","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.213Z"}
2025-08-02 11:23:21.299 [ERROR] [GlobalExceptionFilter] GET /api/teacher-task/list?studentId=5&roleId=2&page=1&size=50 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.301 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/teacher-task/list?studentId=5&roleId=2&page=1&size=50","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.299Z"}
2025-08-02 11:23:21.314 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/list - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.316 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/list","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.315Z"}
2025-08-02 11:23:21.333 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.335 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.335Z"}
2025-08-02 11:23:21.348 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=5 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.349 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/packages?userId=5","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.349Z"}
2025-08-02 11:23:21.362 [ERROR] [GlobalExceptionFilter] GET /api/web/announcement/publishedIdsByTarget - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.363 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/publishedIdsByTarget","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.363Z"}
2025-08-02 11:23:21.383 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.384 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.384Z"}
2025-08-02 11:23:21.400 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.401 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.401Z"}
2025-08-02 11:23:21.416 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=5 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.434 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/packages?userId=5","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.423Z"}
2025-08-02 11:23:21.471 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.472 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.472Z"}
2025-08-02 11:23:21.482 [ERROR] [GlobalExceptionFilter] GET /api/encryption/publicKey - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.484 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/encryption/publicKey","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.483Z"}
2025-08-02 11:23:21.502 [ERROR] [GlobalExceptionFilter] GET /api/user-point/packages?userId=5 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.503 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/packages?userId=5","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.502Z"}
2025-08-02 11:23:21.510 [ERROR] [GlobalExceptionFilter] GET /api/user-point/total - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:127:15)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-08-02 11:23:21.512 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/user-point/total","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"登录已过期","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.511Z"}
2025-08-02 11:23:21.519 [ERROR] [Console] ❌ Redis中没有找到用户信息，key: user:token:5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
2025-08-02 11:23:21.521 [ERROR] [Console] ❌ 刷新token异常: {
  "response": {
    "code": 401,
    "msg": "用户信息不存在",
    "data": null
  },
  "status": 401,
  "message": "Http Exception",
  "name": "HttpException"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
2025-08-02 11:23:21.529 [ERROR] [GlobalExceptionFilter] POST /api/router-guard/refresh-token - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.refreshToken (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:203:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async RouterGuardController.refreshToken (F:\logicleap2\logic-back\src\web\router_guard\router-guard.controller.ts:63:22)
2025-08-02 11:23:21.530 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/router-guard/refresh-token","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"用户信息不存在","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:23:21.530Z"}
2025-08-02 11:23:22.039 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:31:04.159 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:52:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:227:20)
2025-08-02 11:31:04.160 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-08-02 11:31:04.161 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - 获取当前IP位置失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
Error: 获取当前IP位置失败
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:230:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-08-02 11:31:04.162 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":108428,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"获取当前IP位置失败","details":{"name":"Error","stack":"Error: 获取当前IP位置失败\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:230:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-08-02T03:31:04.161Z"}
2025-08-02 11:34:35.098 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 11:34:35.213 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.213 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.214 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.215 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.215 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.215 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.216 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.216 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.217 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.217 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.218 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.218 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.219 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.219 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.220 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.220 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.221 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.221 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.222 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:35.223 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95544,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.097 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 11:34:49.191 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.191 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.192 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.192 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.193 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.193 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.193 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.194 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.194 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.195 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.195 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.195 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.198 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.198 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.199 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.199 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.200 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.200 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.201 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:34:49.201 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:37:03.039 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:52:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-08-02 11:37:03.039 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-08-02 11:37:03.042 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - 查询失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
Error: 查询失败
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-08-02 11:37:03.043 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"查询失败","details":{"name":"Error","stack":"Error: 查询失败\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-08-02T03:37:03.042Z"}
2025-08-02 11:45:57.051 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:52:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:220:20)
2025-08-02 11:45:57.052 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-08-02 11:45:57.052 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - 获取当前IP位置失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
Error: 获取当前IP位置失败
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:223:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-08-02 11:45:57.053 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"获取当前IP位置失败","details":{"name":"Error","stack":"Error: 获取当前IP位置失败\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:223:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-08-02T03:45:57.053Z"}
2025-08-02 11:47:15.846 [ERROR] [GlobalExceptionFilter] GET /api/web/user/info/4340 - read ECONNRESET {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
QueryFailedError: read ECONNRESET
    at Query.onResult (F:\logicleap2\logic-back\node_modules\typeorm\driver\src\driver\mysql\MysqlQueryRunner.ts:243:33)
    at PoolConnection._notifyError (F:\logicleap2\logic-back\node_modules\mysql2\lib\base\connection.js:223:21)
    at PoolConnection._handleFatalError (F:\logicleap2\logic-back\node_modules\mysql2\lib\base\connection.js:178:10)
    at PoolConnection._handleNetworkError (F:\logicleap2\logic-back\node_modules\mysql2\lib\base\connection.js:191:10)
    at Socket.emit (node:events:519:28)
    at emitErrorNT (node:internal/streams/destroy:169:8)
    at emitErrorCloseNT (node:internal/streams/destroy:128:3)
    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)
2025-08-02 11:47:15.846 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":100888,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/user/info/4340","method":"GET","statusCode":500,"message":"read ECONNRESET","details":{"name":"QueryFailedError","stack":"QueryFailedError: read ECONNRESET\n    at Query.onResult (F:\\logicleap2\\logic-back\\node_modules\\typeorm\\driver\\src\\driver\\mysql\\MysqlQueryRunner.ts:243:33)\n    at PoolConnection._notifyError (F:\\logicleap2\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:223:21)\n    at PoolConnection._handleFatalError (F:\\logicleap2\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:178:10)\n    at PoolConnection._handleNetworkError (F:\\logicleap2\\logic-back\\node_modules\\mysql2\\lib\\base\\connection.js:191:10)\n    at Socket.emit (node:events:519:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-08-02T03:47:15.846Z"}
2025-08-02 11:57:07.948 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 11:57:08.041 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.041 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.041 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.042 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.042 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.043 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.043 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.043 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.044 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.044 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.044 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.045 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.046 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.046 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.046 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.047 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.047 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.047 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.048 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:08.049 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.642 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 11:57:25.740 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.740 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.741 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.741 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.741 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.742 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.742 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.742 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.743 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.743 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.744 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.744 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.744 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.745 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.745 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.745 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.746 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.746 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.747 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:25.747 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54872,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.106 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 11:57:44.199 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.199 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.200 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.201 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.201 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.201 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.202 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.202 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.203 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.203 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.204 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.204 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.204 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.205 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.205 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.206 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.206 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.206 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.207 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 11:57:44.207 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":45812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.735 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:02:33.855 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.855 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.856 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.856 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.857 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.857 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.858 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.858 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.859 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.859 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.860 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.860 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.861 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.861 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.861 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.862 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.862 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.862 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.863 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:02:33.864 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:14.616 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:03:14.619 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:14.620 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-14606c70 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107572,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-14606c70\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:03:44.185 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:03:44.320 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.320 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.321 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.322 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.322 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.323 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.323 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.324 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.324 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.325 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.325 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.326 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.326 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.327 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.327 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.327 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.328 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.328 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.329 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:03:44.329 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106912,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.523 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:04:15.654 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.654 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.655 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.655 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.656 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.657 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.657 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.658 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.658 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.659 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.659 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.660 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.660 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.661 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.661 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.662 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.665 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.667 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.670 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:15.671 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:16.179 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:04:16.182 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:16.183 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:04:16.184 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":99212,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:04:55.067 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:04:55.171 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.171 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.172 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.172 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.173 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.173 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.173 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.174 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.174 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.174 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.175 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.175 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.176 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.176 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.179 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.180 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.180 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.181 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.182 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:04:55.182 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:05:16.616 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:05:16.619 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:05:16.620 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:05:43.607 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:05:43.609 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:05:43.609 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:05:43.610 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:06:17.605 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:06:17.610 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:06:17.611 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:06:44.594 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:06:44.596 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:06:44.597 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:06:44.599 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:07:18.603 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:07:18.610 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:07:18.612 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:07:45.601 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:07:45.603 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:07:45.604 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:07:45.604 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:08:19.594 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:08:19.597 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:08:19.598 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:08:19.598 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:08:46.592 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:08:46.595 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:08:46.596 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:08:46.596 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:09:20.593 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:09:20.595 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:09:20.595 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:09:20.595 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:09:47.592 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:09:47.595 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:09:47.595 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:09:47.596 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:10:21.596 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:10:21.600 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:10:21.600 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:10:48.592 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:10:48.595 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:10:48.596 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:11:22.596 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:11:22.599 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:11:22.599 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:11:49.606 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:11:49.608 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:11:49.609 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:12:23.598 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:12:23.602 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:12:23.603 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:12:50.592 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:12:50.594 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:12:50.594 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:12:50.595 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:13:24.603 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:13:24.606 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:13:24.607 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:13:51.597 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:13:51.599 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:13:51.600 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:14:25.592 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:14:25.594 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:14:25.596 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:14:52.592 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:14:52.593 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:14:52.594 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:14:52.595 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:15:26.594 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:15:26.595 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:15:26.596 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:15:26.596 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:15:53.604 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:15:53.608 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:15:53.609 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:16:27.595 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:16:27.597 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:16:27.597 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:16:27.598 [ERROR] [EncryptionService] 【会话创建】创建会话密钥失败: error:02000084:rsa routines::data too large for modulus {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000084:rsa routines::data too large for modulus\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:16:54.864 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:16:54.869 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:16:54.870 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:17:28.594 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:17:28.597 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:17:28.599 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:17:55.595 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:17:55.599 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:17:55.599 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:18:29.596 [ERROR] [KeyManagementService] RSA私钥(ID:key-dc201b71)解密操作失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: error:02000079:rsa routines::oaep decoding error\n    at Object.privateDecrypt (node:internal/crypto/cipher:79:12)\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:653:34)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:18:29.599 [ERROR] [KeyManagementService] 使用活跃密钥重试解密仍然失败: error:02000079:rsa routines::oaep decoding error {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:18:29.600 [ERROR] [KeyManagementService] 使用RSA私钥解密失败: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101024,"hostname":"DESKTOP-1L38AEG","stack":["Error: RSA解密失败，可能是密钥不匹配。请求密钥ID: key-dc201b71, 活跃密钥ID: key-e0fbe8f1\n    at KeyManagementService.decryptWithRsaPrivateKeyById (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:690:17)\n    at EncryptionService.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:140:52)\n    at EncryptionController.createSession (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.controller.ts:95:51)\n    at F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\router\\router-execution-context.js:38:29\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"]}
2025-08-02 12:21:52.889 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:21:53.006 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.006 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.007 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.007 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.008 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.008 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.008 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.009 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.009 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.010 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.011 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.011 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.011 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.012 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.012 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.013 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.013 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.013 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.014 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:21:53.014 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:24:39.915 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":1}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:52:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:220:20)
2025-08-02 12:24:39.916 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-08-02 12:24:39.918 [ERROR] [API_RESPONSE] [ip-loc-1754108679917-xcgr7vc30] API IpLocation.queryLocation Failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG","module":"IpLocation","operation":"queryLocation","success":false,"executionTime":"11ms","traceId":"ip-loc-1754108679917-xcgr7vc30","requestData":"{\"ip\":\"127.0.0.1\",\"includeRisk\":false}","responseData":"undefined","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:52:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:220:20)
2025-08-02 12:24:39.920 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - 获取当前IP位置失败 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG"}
Error: 获取当前IP位置失败
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:223:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-08-02 12:24:39.927 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106368,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"获取当前IP位置失败","details":{"name":"Error","stack":"Error: 获取当前IP位置失败\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:223:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-08-02T04:24:39.926Z"}
2025-08-02 12:30:12.915 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:30:13.008 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.009 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.009 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.010 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.010 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.010 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.011 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.011 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.011 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.012 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.012 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.013 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.013 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.013 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.014 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.014 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.014 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.015 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.015 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:13.016 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":109244,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.636 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:30:50.745 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.745 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.745 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.746 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.746 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.747 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.747 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.748 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.748 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.749 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.749 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.749 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.750 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.750 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.751 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.751 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.751 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.752 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.752 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:30:50.753 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":107204,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:11.991 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:31:12.124 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.125 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.126 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.127 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.128 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.129 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.129 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.130 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.130 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.131 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.131 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.132 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.132 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.133 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.134 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.135 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.135 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.136 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.137 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:12.137 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":101588,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.805 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:31:41.905 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.905 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.905 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.906 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.906 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.907 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.907 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.907 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.908 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.908 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.908 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.909 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.909 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.910 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.910 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.911 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.911 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.911 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.912 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:31:41.912 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95976,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.217 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:32:14.309 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.309 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.310 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.310 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.310 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.311 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.311 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.311 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.312 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.312 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.312 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.313 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.313 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.313 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.314 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.314 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.314 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.315 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.315 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:14.316 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":95956,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.730 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-08-02 12:32:45.813 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.813 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.813 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.814 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.814 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.814 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.815 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.815 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.815 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.816 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.816 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.817 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.818 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.818 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.819 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.819 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.820 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.821 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.821 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-08-02 12:32:45.821 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":106764,"hostname":"DESKTOP-1L38AEG","stack":[null]}

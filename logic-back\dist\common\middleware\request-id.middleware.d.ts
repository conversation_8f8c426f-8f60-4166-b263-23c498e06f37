import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
export declare class RequestIdMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: NextFunction): void;
    private generateRequestId;
    private generateTraceId;
}
export declare class RequestIdUtils {
    static getRequestId(req: Request): string;
    static getTraceId(req: Request): string;
    static generateRequestId(): string;
    static generateTraceId(): string;
    static generateBusinessRequestId(prefix: string): string;
}

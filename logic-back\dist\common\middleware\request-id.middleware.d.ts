import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
export declare class RequestIdMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: NextFunction): void;
    private generateTraceId;
}
export declare enum BusinessModule {
    IP_LOCATION = "ip-loc",
    PAYMENT = "payment",
    USER = "user",
    AUTH = "auth",
    COURSE = "course",
    TASK = "task",
    PACKAGE = "package",
    SCRATCH = "scratch",
    TPS = "tps",
    GENERAL = "req"
}
export declare class RequestIdUtils {
    static getRequestId(req: Request): string;
    static getTraceId(req: Request): string;
    static getBusinessRequestId(req: Request, fallbackModule?: BusinessModule): string;
    static generateRequestId(): string;
    static generateTraceId(): string;
    static generateBusinessRequestId(module: BusinessModule): string;
    static inferBusinessModule(path: string): BusinessModule;
    static isFromModule(requestId: string, module: BusinessModule): boolean;
    static extractModulePrefix(requestId: string): string;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IpLocationFacadeService = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("../../../../common/logger/logger.service");
const ip_location_application_service_1 = require("./ip-location-application.service");
const ip_location_domain_service_1 = require("../../domain/services/ip-location-domain.service");
const http_response_result_service_1 = require("../../../../web/http_response_result/http_response_result.service");
const request_trace_service_1 = require("../../../../web/http_response_result/request-trace.service");
let IpLocationFacadeService = class IpLocationFacadeService {
    applicationService;
    domainService;
    logger;
    httpResponseService;
    requestTraceService;
    constructor(applicationService, domainService, logger, httpResponseService, requestTraceService) {
        this.applicationService = applicationService;
        this.domainService = domainService;
        this.logger = logger;
        this.httpResponseService = httpResponseService;
        this.requestTraceService = requestTraceService;
    }
    async getLocationByIP(ip, includeRisk = false) {
        const startTime = Date.now();
        try {
            const request = { ip, includeRisk };
            const result = await this.applicationService.queryIpLocation(request);
            return this.httpResponseService.enhancedSuccess(result, '查询成功', 200, {
                executionTime: Date.now() - startTime,
                fromCache: false,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0'
                },
                logContext: {
                    module: 'IpLocation',
                    operation: 'queryLocation',
                    requestData: { ip, includeRisk }
                }
            });
        }
        catch (error) {
            this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.enhancedError(error.message || '查询失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0'
                },
                logContext: {
                    module: 'IpLocation',
                    operation: 'queryLocation',
                    requestData: { ip, includeRisk },
                    error: error
                }
            });
        }
    }
    async assessLoginRisk(userId, ip, userAgent, sessionId) {
        const startTime = Date.now();
        try {
            const request = {
                userId,
                ipAddress: ip,
                userAgent,
                sessionId
            };
            const result = await this.applicationService.checkLoginRisk(request);
            return this.httpResponseService.enhancedSuccess(result, '风险评估完成', 200, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString()
                },
                logContext: {
                    module: 'IpLocation',
                    operation: 'assessLoginRisk',
                    userId: userId.toString(),
                    requestData: { userId, ip, userAgent, sessionId }
                }
            });
        }
        catch (error) {
            this.logger.error(`登录风险评估失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.enhancedError(error.message || '风险评估失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString()
                }
            });
        }
    }
    async getUserLocationStats(userId, days = 30) {
        const startTime = Date.now();
        try {
            const result = await this.applicationService.getUserLocationStatistics(userId, days);
            return this.httpResponseService.enhancedSuccess(result, '统计查询成功', 200, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString(),
                    queryDays: days.toString()
                }
            });
        }
        catch (error) {
            this.logger.error(`用户位置统计失败: ${userId}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.enhancedError(error.message || '统计查询失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString()
                }
            });
        }
    }
    async setTrustedLocation(userId, province, city, reason = '用户主动设置') {
        const startTime = Date.now();
        try {
            const request = {
                province,
                city,
                reason
            };
            await this.applicationService.setTrustedLocation(userId, request);
            return this.httpResponseService.enhancedSuccess({ userId, province, city, reason }, '可信位置设置成功', 200, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString(),
                    operation: 'setTrustedLocation'
                }
            });
        }
        catch (error) {
            this.logger.error(`设置可信位置失败: ${userId}-${province}-${city}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.enhancedError(error.message || '设置失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString(),
                    operation: 'setTrustedLocation'
                }
            });
        }
    }
    async updateUserCommonLocation(userId, ip) {
        const startTime = Date.now();
        try {
            const request = { ip, includeRisk: false };
            const locationInfo = await this.applicationService.queryIpLocation(request);
            return this.httpResponseService.enhancedSuccess({ userId, ip, location: locationInfo }, '常用位置更新成功', 200, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString(),
                    operation: 'updateUserCommonLocation'
                }
            });
        }
        catch (error) {
            this.logger.error(`更新常用位置失败: ${userId}-${ip}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.enhancedError(error.message || '更新失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    userId: userId.toString(),
                    operation: 'updateUserCommonLocation'
                }
            });
        }
    }
    async testIpResolution(testIp = '*******') {
        const startTime = Date.now();
        try {
            const result = await this.domainService.testResolution(testIp);
            return this.httpResponseService.enhancedSuccess(result, result.success ? '测试成功' : '测试失败', 200, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    testIp,
                    operation: 'testIpResolution'
                }
            });
        }
        catch (error) {
            this.logger.error(`IP解析测试失败: ${testIp}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.enhancedError(error.message || '测试失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: {
                    requestId: this.generateRequestId(),
                    version: 'v2.0.0',
                    testIp,
                    operation: 'testIpResolution'
                }
            });
        }
    }
    async getLocationByIPUnified(ip, includeRisk = false) {
        try {
            const request = { ip, includeRisk };
            const result = await this.applicationService.queryIpLocation(request);
            return this.httpResponseService.unifiedSuccess(result, '查询成功', 200, {
                fromCache: false
            });
        }
        catch (error) {
            this.logger.error(`IP位置查询失败: ${ip}`, error, 'IpLocationFacadeService');
            return this.httpResponseService.unifiedError(error.message || '查询失败', null, 500, {
                errorDetails: { ip, includeRisk },
                stack: error.stack
            });
        }
    }
    async checkUserPermission(userId) {
        if (userId < 1000) {
            return this.httpResponseService.businessError(3001, '您没有权限访问IP地理位置服务', { userId, requiredRole: 'admin' }, {
                errorDetails: { reason: 'User ID too low', minRequiredId: 1000 }
            });
        }
        return this.httpResponseService.unifiedSuccess({ userId, hasPermission: true }, '权限验证通过', 200);
    }
    generateRequestId() {
        if (this.requestTraceService) {
            return this.requestTraceService.generateBusinessRequestId('ip-loc');
        }
        return `ip-loc-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    async getCacheStatus(_key) {
        return false;
    }
    logPerformanceMetrics(operation, executionTime, success) {
        if (executionTime > 1000) {
            this.logger.warn(`慢操作检测: ${operation} 耗时 ${executionTime}ms - 成功: ${success}`, 'IpLocationFacadeService');
        }
    }
};
exports.IpLocationFacadeService = IpLocationFacadeService;
exports.IpLocationFacadeService = IpLocationFacadeService = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, common_1.Optional)()),
    __metadata("design:paramtypes", [ip_location_application_service_1.IpLocationApplicationService,
        ip_location_domain_service_1.IpLocationDomainService,
        logger_service_1.LoggerService,
        http_response_result_service_1.HttpResponseResultService,
        request_trace_service_1.RequestTraceService])
], IpLocationFacadeService);
//# sourceMappingURL=ip-location-facade.service.js.map
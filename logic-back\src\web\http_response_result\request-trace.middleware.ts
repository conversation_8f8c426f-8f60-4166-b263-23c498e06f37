import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RequestTraceService } from './request-trace.service';

/**
 * 请求追踪中间件
 * 在每个请求开始时初始化追踪信息
 * 确保整个请求生命周期中使用相同的requestId和traceId
 */
@Injectable()
export class RequestTraceMiddleware implements NestMiddleware {
  constructor(private readonly requestTraceService: RequestTraceService) {}

  use(req: Request, res: Response, next: NextFunction) {
    // 初始化请求追踪信息
    this.requestTraceService.initializeFromRequest(req);

    // 获取追踪信息
    const { requestId, traceId } = this.requestTraceService.getTraceInfo();

    // 在响应头中添加追踪信息，方便前端和调试
    res.setHeader('X-Request-ID', requestId);
    res.setHeader('X-Trace-ID', traceId);

    // 记录请求开始日志（可选）
    console.log(`[${traceId}] ${req.method} ${req.url} - Request started`);

    // 继续处理请求
    next();
  }
}

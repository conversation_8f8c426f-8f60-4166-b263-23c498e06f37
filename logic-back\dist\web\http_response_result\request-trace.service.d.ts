import { Request } from 'express';
export declare class RequestTraceService {
    private _requestId;
    private _traceId;
    private _startTime;
    private _request;
    constructor();
    initializeFromRequest(request: Request): void;
    getRequestId(): string;
    getTraceId(): string;
    getPath(): string;
    getExecutionTime(): number;
    getTraceInfo(): {
        requestId: string;
        traceId: string;
        path: string;
        executionTime: number;
    };
    private generateRequestId;
    private generateTraceId;
    generateBusinessRequestId(prefix: string): string;
    setCustomRequestId(requestId: string): void;
    setCustomTraceId(traceId: string): void;
    getClientIP(): string;
    getUserAgent(): string;
    getMethod(): string;
    getRequestContext(): {
        requestId: string;
        traceId: string;
        path: string;
        method: string;
        clientIP: string;
        userAgent: string;
        executionTime: number;
        timestamp: string;
    };
    reset(): void;
}

/**
 * 响应日志记录配置
 */
export interface ResponseLoggingConfig {
  /** 是否启用详细日志记录 */
  enableDetailedLogging: boolean;
  
  /** 是否记录请求体 */
  logRequestBody: boolean;
  
  /** 是否记录响应体 */
  logResponseBody: boolean;
  
  /** 慢请求阈值（毫秒） */
  slowRequestThreshold: number;
  
  /** 需要脱敏的字段列表 */
  sensitiveFields: string[];
  
  /** 需要排除日志记录的路径 */
  excludePaths: string[];
  
  /** 需要详细记录的路径 */
  detailedLogPaths: string[];
  
  /** 最大请求体大小（字节） */
  maxRequestBodySize: number;
  
  /** 最大响应体大小（字节） */
  maxResponseBodySize: number;
}

/**
 * 默认响应日志配置
 */
export const DEFAULT_RESPONSE_LOGGING_CONFIG: ResponseLoggingConfig = {
  enableDetailedLogging: true,
  logRequestBody: true,
  logResponseBody: true,
  slowRequestThreshold: 1000, // 1秒
  sensitiveFields: [
    'password', 'oldPassword', 'newPassword',
    'token', 'accessToken', 'refreshToken',
    'cardNumber', 'cvv', 'bankAccount', 'qrCode',
    'secret', 'key', 'privateKey', 'apiKey'
  ],
  excludePaths: [
    '/health',
    '/metrics',
    '/favicon.ico',
    '/weixin/message' // 微信消息接口
  ],
  detailedLogPaths: [
    '/api/v1/ip-location',
    '/api/v1/payment',
    '/api/v1/auth',
    '/api/v1/user'
  ],
  maxRequestBodySize: 10 * 1024, // 10KB
  maxResponseBodySize: 50 * 1024  // 50KB
};

/**
 * 响应日志级别
 */
export enum ResponseLogLevel {
  /** 基础日志：只记录基本信息 */
  BASIC = 'basic',
  
  /** 详细日志：记录请求响应详情 */
  DETAILED = 'detailed',
  
  /** 调试日志：记录所有信息 */
  DEBUG = 'debug'
}

/**
 * 日志上下文接口
 */
export interface LogContext {
  /** 模块名称 */
  module: string;
  
  /** 操作名称 */
  operation: string;
  
  /** 用户ID */
  userId?: string;
  
  /** 追踪ID */
  traceId?: string;
  
  /** 请求数据 */
  requestData?: any;
  
  /** 错误信息 */
  error?: Error;
  
  /** 额外的上下文信息 */
  extra?: Record<string, any>;
}

/**
 * 响应日志工具类
 */
export class ResponseLoggingUtils {
  /**
   * 检查路径是否需要排除日志记录
   */
  static shouldExcludePath(path: string, config: ResponseLoggingConfig): boolean {
    return config.excludePaths.some(excludePath => path.includes(excludePath));
  }

  /**
   * 检查路径是否需要详细日志记录
   */
  static shouldDetailedLog(path: string, config: ResponseLoggingConfig): boolean {
    return config.detailedLogPaths.some(detailedPath => path.startsWith(detailedPath));
  }

  /**
   * 检查是否为慢请求
   */
  static isSlowRequest(responseTime: number, config: ResponseLoggingConfig): boolean {
    return responseTime > config.slowRequestThreshold;
  }

  /**
   * 截断过大的数据
   */
  static truncateData(data: any, maxSize: number, type: 'request' | 'response'): any {
    if (!data) return data;

    const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
    
    if (dataStr.length > maxSize) {
      const truncated = dataStr.substring(0, maxSize);
      return {
        ...data,
        _truncated: true,
        _originalSize: dataStr.length,
        _truncatedAt: maxSize,
        _type: type
      };
    }

    return data;
  }

  /**
   * 生成日志标签
   */
  static generateLogTags(req: any, res: any, responseTime: number, config: ResponseLoggingConfig): string[] {
    const tags: string[] = [];

    // 请求方法标签
    tags.push(`method:${req.method}`);

    // 状态码标签
    if (res.statusCode >= 200 && res.statusCode < 300) {
      tags.push('status:success');
    } else if (res.statusCode >= 400 && res.statusCode < 500) {
      tags.push('status:client_error');
    } else if (res.statusCode >= 500) {
      tags.push('status:server_error');
    }

    // 性能标签
    if (this.isSlowRequest(responseTime, config)) {
      tags.push('performance:slow');
    } else if (responseTime < 100) {
      tags.push('performance:fast');
    } else {
      tags.push('performance:normal');
    }

    // 路径类型标签
    if (this.shouldDetailedLog(req.url, config)) {
      tags.push('log_type:detailed');
    } else {
      tags.push('log_type:basic');
    }

    return tags;
  }

  /**
   * 格式化日志消息
   */
  static formatLogMessage(
    req: any, 
    res: any, 
    responseTime: number, 
    traceId?: string
  ): string {
    const prefix = traceId ? `[${traceId}]` : '';
    const method = req.method;
    const url = req.url;
    const status = res.statusCode;
    const time = `${responseTime}ms`;

    return `${prefix} ${method} ${url} ${status} ${time}`;
  }
}

/**
 * 敏感数据脱敏工具
 */
export class DataSanitizer {
  /**
   * 脱敏敏感字段
   */
  static sanitize(data: any, sensitiveFields: string[]): any {
    if (!data || typeof data !== 'object') return data;

    const sanitized = Array.isArray(data) ? [...data] : { ...data };

    for (const key in sanitized) {
      if (sanitized.hasOwnProperty(key)) {
        if (this.isSensitiveField(key, sensitiveFields)) {
          sanitized[key] = this.maskValue(sanitized[key]);
        } else if (typeof sanitized[key] === 'object') {
          sanitized[key] = this.sanitize(sanitized[key], sensitiveFields);
        }
      }
    }

    return sanitized;
  }

  /**
   * 检查是否为敏感字段
   */
  private static isSensitiveField(fieldName: string, sensitiveFields: string[]): boolean {
    const lowerFieldName = fieldName.toLowerCase();
    return sensitiveFields.some(sensitive => 
      lowerFieldName.includes(sensitive.toLowerCase())
    );
  }

  /**
   * 脱敏值
   */
  private static maskValue(value: any): string {
    if (typeof value === 'string') {
      if (value.length <= 4) {
        return '***';
      } else if (value.length <= 8) {
        return value.substring(0, 2) + '***' + value.substring(value.length - 2);
      } else {
        return value.substring(0, 4) + '***' + value.substring(value.length - 4);
      }
    }
    return '***MASKED***';
  }
}

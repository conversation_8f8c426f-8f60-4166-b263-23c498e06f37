{"version": 3, "file": "logger.service.js", "sourceRoot": "", "sources": ["../../../src/common/logger/logger.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAgF;AAChF,qCAAiC;AACjC,+CAAuD;AACvD,2CAAwC;AAGjC,IAAM,aAAa,GAAnB,MAAM,aAAa;IAE4B;IADpD,YACoD,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IAKJ,GAAG,CAAC,OAAY,EAAE,OAAgB;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAKD,KAAK,CAAC,OAAY,EAAE,KAAc,EAAE,OAAgB;QAClD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IACjD,CAAC;IAKD,IAAI,CAAC,OAAY,EAAE,OAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACzC,CAAC;IAKD,KAAK,CAAC,OAAY,EAAE,OAAgB;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC1C,CAAC;IAKD,OAAO,CAAC,OAAY,EAAE,OAAgB;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC;IAKD,cAAc,CAAC,GAAQ,EAAE,GAAQ,EAAE,YAAoB;QACrD,MAAM,OAAO,GAAG;YACd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY,EAAE,GAAG,YAAY,IAAI;YACjC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa;YAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE;YAC/B,OAAO,EAAE,MAAM;YACf,GAAG,OAAO;SACX,CAAC,CAAC;IACL,CAAC;IAKD,sBAAsB,CACpB,GAAQ,EACR,GAAQ,EACR,YAAoB,EACpB,OAKC;QAED,MAAM,OAAO,GAAG;YAEd,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,GAAG;YACZ,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,YAAY,EAAE,GAAG,YAAY,IAAI;YACjC,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa;YAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YAGnC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE;YAGlD,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC;YACnE,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC;YAGtE,SAAS,EAAE,GAAG,CAAC,UAAU,IAAI,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG;YACxD,OAAO,EAAE,GAAG,CAAC,UAAU,IAAI,GAAG;YAC9B,aAAa,EAAE,YAAY,GAAG,IAAI;SACnC,CAAC;QAGF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,uBAAuB,EAAE;gBAC5D,OAAO,EAAE,eAAe;gBACxB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,qBAAqB,EAAE;gBACzD,OAAO,EAAE,eAAe;gBACxB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,wBAAwB,EAAE;gBAC5D,OAAO,EAAE,eAAe;gBACxB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,SAAiB,EAAE,KAAa,EAAE,IAAU,EAAE,KAAW;QACnE,MAAM,OAAO,GAAG;YACd,SAAS;YACT,KAAK;YACL,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,SAAS,cAAc,KAAK,EAAE,EAAE;gBAC5D,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,SAAS,OAAO,KAAK,EAAE,EAAE;gBACpD,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,MAAc,EAAE,IAAS,EAAE,KAAW;QAC/C,MAAM,OAAO,GAAG;YACd,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,MAAM,SAAS,EAAE;gBAC5C,OAAO,EAAE,SAAS;gBAClB,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,MAAM,EAAE,EAAE;gBACpC,OAAO,EAAE,SAAS;gBAClB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,OAAO,CAAC,MAAc,EAAE,MAAe,EAAE,OAAa,EAAE,KAAW;QACjE,MAAM,OAAO,GAAG;YACd,MAAM;YACN,MAAM;YACN,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;YACtD,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,MAAM,SAAS,EAAE;gBACzC,OAAO,EAAE,MAAM;gBACf,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,EAAE,EAAE;gBACjC,OAAO,EAAE,MAAM;gBACf,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,WAAW,CAAC,MAAc,EAAE,MAAc,EAAE,IAAU,EAAE,KAAW;QACjE,MAAM,OAAO,GAAG;YACd,MAAM;YACN,MAAM;YACN,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;YAC7C,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,MAAM,IAAI,MAAM,SAAS,EAAE;gBACvD,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;gBACV,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,MAAM,IAAI,MAAM,EAAE,EAAE;gBAC/C,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,UAAU,CAAC,OAAe,EAAE,OAAa;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;YACxB,OAAO,EAAE,SAAS;YAClB,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAKD,cAAc,CACZ,MAAc,EACd,SAAiB,EACjB,OAQC;QAED,MAAM,OAAO,GAAG;YACd,MAAM;YACN,SAAS;YACT,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,SAAS;YAC/E,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe,EAAE;YAClD,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YACtD,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,YAAY,CAAC;YACxD,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACxD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,OAAO,SAAS,MAAM,IAAI,SAAS,UAAU,EAAE;gBAC1E,OAAO,EAAE,cAAc;gBACvB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,SAAS,MAAM,IAAI,SAAS,SAAS,EAAE;gBAC1E,OAAO,EAAE,cAAc;gBACvB,GAAG,OAAO;gBACV,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,KAAK;aAC5B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAOO,eAAe;QACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;IAC9E,CAAC;IAKO,mBAAmB,CAAC,IAAS,EAAE,GAAW;QAChD,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAC;QAEnD,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;QAG9B,MAAM,eAAe,GAAG,CAAC,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAC3G,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrB,SAAS,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;YACpC,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC7B,MAAM,sBAAsB,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAC9E,sBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrC,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrB,SAAS,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;gBACpC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAKO,oBAAoB,CAAC,IAAS,EAAE,GAAW;QACjD,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAEvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAGlE,IAAI,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpD,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACrC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC;gBACrC,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC;gBAC5C,CAAC;YACH,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAE7B,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;oBACtC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC;gBACtC,CAAC;gBACD,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;oBAEvC,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;oBACpC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACvB,MAAM,CAAC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAChG,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKO,eAAe,CAAC,IAAS;QAC/B,IAAI,CAAC,IAAI;YAAE,OAAO,WAAW,CAAC;QAE9B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAGhE,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,sBAAsB,CAAC;QAChC,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,GAAQ;QAChC,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO;QAE5C,MAAM,aAAa,GAAG;YACpB,UAAU,EAAE,aAAa,EAAE,aAAa;YACxC,OAAO,EAAE,aAAa,EAAE,cAAc;YACtC,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ;YAC5C,QAAQ,EAAE,KAAK,EAAE,YAAY;SAC9B,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACtB,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC5B,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;oBACzF,GAAG,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC;gBAC5B,CAAC;qBAAM,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;oBACxC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAKD,cAAc,CAAC,SAAiB,EAAE,QAAgB,EAAE,OAAa;QAC/D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,SAAS,SAAS,QAAQ,IAAI,EAAE;YAC/D,OAAO,EAAE,aAAa;YACtB,SAAS;YACT,QAAQ;YACR,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAKD,WAAW,CAAC,KAAa,EAAE,OAAY,EAAE,WAAsC,QAAQ;QACrF,MAAM,OAAO,GAAG;YACd,KAAK;YACL,QAAQ;YACR,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YACxB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,EAAE,EAAE;gBAC5C,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,KAAK,EAAE,EAAE;gBAC3C,OAAO,EAAE,UAAU;gBACnB,GAAG,OAAO;aACX,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF,CAAA;AA/aY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;GAFvD,aAAa,CA+azB"}
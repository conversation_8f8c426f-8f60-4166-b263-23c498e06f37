# 统一错误响应迁移指南

## 📋 概述

本指南详细说明如何从现有的错误响应格式平滑迁移到新的统一错误响应格式，确保向后兼容性。

## 🎯 迁移策略

### 阶段一：向后兼容扩展（当前阶段）
- ✅ 保持现有 `{ code, msg, data }` 格式
- ✅ 新增统一响应方法
- ✅ 创建统一异常类
- ✅ 提供迁移工具

### 阶段二：逐步迁移（推荐）
- 🔄 新API使用统一格式
- 🔄 现有API逐步升级
- 🔄 前端适配新格式

### 阶段三：完全统一（未来）
- 🚀 所有API使用统一格式
- 🚀 移除旧的响应方法
- 🚀 完善监控和日志

## 💻 使用方式

### 1. **保持现有代码不变（向后兼容）**

```typescript
// 现有代码继续正常工作
@Controller('api/v1/users')
export class UserController {
  constructor(private readonly httpResponseService: HttpResponseResultService) {}

  @Get()
  async getUsers() {
    const users = await this.userService.findAll();
    
    // 原有方法继续可用
    return this.httpResponseService.success(users, '查询成功');
    // 返回: { code: 200, msg: '查询成功', data: users }
  }
}
```

### 2. **新API使用统一响应格式**

```typescript
@Controller('api/v1/users')
export class UserController {
  constructor(private readonly httpResponseService: HttpResponseResultService) {}

  @Post()
  async createUser(@Body() createUserDto: CreateUserDto) {
    try {
      const user = await this.userService.create(createUserDto);
      
      // 使用新的统一响应方法
      return this.httpResponseService.unifiedSuccess(
        user,
        '用户创建成功',
        201,
        {
          requestId: this.generateRequestId(),
          path: '/api/v1/users'
        }
      );
    } catch (error) {
      // 使用统一错误响应
      return this.httpResponseService.unifiedError(
        '用户创建失败',
        null,
        400,
        {
          requestId: this.generateRequestId(),
          path: '/api/v1/users',
          errorDetails: { email: createUserDto.email }
        }
      );
    }
  }
}
```

### 3. **使用业务异常类**

```typescript
@Injectable()
export class UserService {
  async findById(id: number): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    
    if (!user) {
      // 使用统一业务异常
      throw UnifiedBusinessException.userNotFound(id, {
        searchedAt: new Date(),
        searchMethod: 'findById'
      });
    }
    
    return user;
  }

  async createUser(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email }
    });
    
    if (existingUser) {
      // 使用静态工厂方法
      throw UnifiedBusinessException.userAlreadyExists(
        createUserDto.email,
        { existingUserId: existingUser.id }
      );
    }
    
    return await this.userRepository.save(createUserDto);
  }
}
```

### 4. **配置统一异常过滤器**

```typescript
// app.module.ts
import { UnifiedExceptionFilter } from './web/http_exception_filter/unified-exception.filter';

@Module({
  // ...
  providers: [
    // 添加统一异常过滤器
    {
      provide: APP_FILTER,
      useClass: UnifiedExceptionFilter,
    },
  ],
})
export class AppModule {}
```

## 📊 响应格式对比

### 现有格式（继续支持）
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": { "id": 123, "name": "用户名" }
}
```

### 统一格式（新增）
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": { "id": 123, "name": "用户名" },
  "success": true,
  "timestamp": "2025-01-15T10:30:00.000Z",
  "trace": {
    "requestId": "req-1705312200000-abc123",
    "path": "/api/v1/users/123"
  }
}
```

### 错误响应（统一格式）
```json
{
  "code": 1001,
  "msg": "用户不存在",
  "data": { "userId": 123 },
  "success": false,
  "timestamp": "2025-01-15T10:30:00.000Z",
  "trace": {
    "requestId": "req-1705312200000-abc123",
    "traceId": "trace-1705312200000-xyz789",
    "path": "/api/v1/users/123"
  },
  "error": {
    "type": "BusinessError",
    "details": {
      "searchedAt": "2025-01-15T10:30:00.000Z",
      "searchMethod": "findById"
    }
  }
}
```

## 🔧 迁移工具

### 1. **响应格式转换器**

```typescript
export class ResponseConverter {
  /**
   * 将现有响应转换为统一格式
   */
  static toUnified<T>(
    legacyResponse: HttpResponse<T>,
    options?: {
      requestId?: string;
      traceId?: string;
      path?: string;
    }
  ): UnifiedResponse<T> {
    return {
      ...legacyResponse,
      success: legacyResponse.code >= 200 && legacyResponse.code < 300,
      timestamp: new Date().toISOString(),
      trace: options ? {
        requestId: options.requestId,
        traceId: options.traceId,
        path: options.path
      } : undefined
    };
  }
}
```

### 2. **异常转换器**

```typescript
export class ExceptionConverter {
  /**
   * 将现有异常转换为统一业务异常
   */
  static toUnified(error: any): UnifiedBusinessException {
    if (error instanceof UnifiedBusinessException) {
      return error;
    }
    
    // 根据错误类型转换
    if (error.message?.includes('用户不存在')) {
      return UnifiedBusinessException.userNotFound();
    }
    
    if (error.message?.includes('权限不足')) {
      return UnifiedBusinessException.permissionDenied();
    }
    
    // 默认转换为系统错误
    return new UnifiedBusinessException(
      5000,
      error.message || '系统错误',
      null,
      { originalError: error.name }
    );
  }
}
```

## 🎯 最佳实践

### 1. **渐进式迁移**
```typescript
// 第一步：在新的API中使用统一格式
@Post('v2/users')  // 新版本API
async createUserV2(@Body() dto: CreateUserDto) {
  return this.httpResponseService.unifiedSuccess(result, '创建成功');
}

// 第二步：保持旧API向后兼容
@Post('users')     // 现有API保持不变
async createUser(@Body() dto: CreateUserDto) {
  return this.httpResponseService.success(result, '创建成功');
}
```

### 2. **错误处理统一**
```typescript
@Injectable()
export class BaseService {
  protected handleError(error: any, context: string): never {
    this.logger.error(`${context} failed`, error);
    
    // 统一错误处理
    if (error instanceof UnifiedBusinessException) {
      throw error;
    }
    
    // 转换为统一异常
    throw ExceptionConverter.toUnified(error);
  }
}
```

### 3. **前端适配**
```typescript
// 前端响应处理适配
interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
  success?: boolean;        // 新增字段
  timestamp?: string;       // 新增字段
  trace?: {                 // 新增字段
    requestId?: string;
    traceId?: string;
    path?: string;
  };
  error?: {                 // 新增字段
    type: string;
    details?: any;
  };
}

// 响应处理函数
function handleApiResponse<T>(response: ApiResponse<T>): T {
  // 兼容新旧格式
  const isSuccess = response.success !== undefined 
    ? response.success 
    : (response.code >= 200 && response.code < 300);
    
  if (isSuccess) {
    return response.data;
  } else {
    // 错误处理，可以利用新的错误信息
    const errorInfo = {
      code: response.code,
      message: response.msg,
      type: response.error?.type,
      details: response.error?.details,
      traceId: response.trace?.traceId
    };
    
    throw new ApiError(errorInfo);
  }
}
```

## 📈 监控和日志

### 1. **响应格式监控**
```typescript
// 监控新旧格式的使用情况
@Injectable()
export class ResponseMonitorService {
  trackResponseFormat(response: any, endpoint: string) {
    const hasUnifiedFields = response.success !== undefined;
    
    this.metricsService.increment('api.response.format', {
      format: hasUnifiedFields ? 'unified' : 'legacy',
      endpoint
    });
  }
}
```

### 2. **错误统计**
```typescript
// 统计错误类型分布
@Injectable()
export class ErrorStatsService {
  trackError(error: UnifiedBusinessException, endpoint: string) {
    this.metricsService.increment('api.error.business', {
      code: error.businessCode.toString(),
      type: error.errorType,
      endpoint
    });
  }
}
```

## 🎉 总结

通过这套向后兼容的迁移方案，你可以：

1. **无缝升级**：现有代码无需修改即可继续工作
2. **渐进迁移**：新功能使用统一格式，旧功能逐步升级
3. **增强功能**：获得更好的错误追踪和调试能力
4. **统一标准**：建立一致的错误处理规范

**推荐的迁移顺序**：
1. 部署新的响应服务和异常过滤器
2. 在新的API中使用统一格式
3. 逐步迁移现有的关键API
4. 前端适配新的响应格式
5. 完善监控和日志系统

这样既保证了系统的稳定性，又为未来的发展奠定了良好的基础！🐱

import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { LoggerService } from './logger.service';
export declare class HttpLoggerMiddleware implements NestMiddleware {
    private readonly loggerService;
    constructor(loggerService: LoggerService);
    use(req: Request, res: Response, next: NextFunction): void;
    private generateTraceId;
}
export declare class EnhancedHttpLoggerMiddleware implements NestMiddleware {
    private readonly loggerService;
    constructor(loggerService: LoggerService);
    use(req: Request, res: Response, next: NextFunction): void;
    private generateTraceId;
}

import { Request } from 'express';
import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
export declare class IpLocationController {
    private readonly ipLocationFacadeService;
    constructor(ipLocationFacadeService: IpLocationFacadeService);
    queryIpLocationV2(query: IpQueryRequestDto): Promise<import("../../../web/http_response_result/http-response.interface").EnhancedHttpResponse<any>>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<import("../../../web/http_response_result/http-response.interface").EnhancedHttpResponse<any>>;
    getUserLocationStats(userId: number, days?: number): Promise<import("../../../web/http_response_result/http-response.interface").EnhancedHttpResponse<any>>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<import("../../../web/http_response_result/http-response.interface").EnhancedHttpResponse<any>>;
    getCurrentIpLocation(request: Request): Promise<import("../../../web/http_response_result/http-response.interface").EnhancedHttpResponse<any>>;
    healthCheck(): Promise<{
        status: string;
        timestamp: string;
        service: string;
    }>;
    private extractClientIP;
    private cleanAndValidateIP;
    private isValidIP;
    private getIpSourceName;
}

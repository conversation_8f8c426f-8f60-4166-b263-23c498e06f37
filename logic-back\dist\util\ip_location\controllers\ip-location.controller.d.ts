import { Request } from 'express';
import { IpLocationFacadeService } from '../application/services/ip-location-facade.service';
import { IpQueryRequestDto } from '../application/dto/requests/ip-query.request.dto';
import { RiskCheckRequestDto } from '../application/dto/requests/risk-check.request.dto';
import { TrustLocationRequestDto } from '../application/dto/requests/trust-location.request.dto';
export declare class IpLocationController {
    private readonly ipLocationFacadeService;
    constructor(ipLocationFacadeService: IpLocationFacadeService);
    queryIpLocationV2(query: IpQueryRequestDto): Promise<{
        success: boolean;
        data: any;
        message: any;
        meta: {
            executionTime: any;
            fromCache: any;
        };
    }>;
    checkLoginRisk(request: RiskCheckRequestDto): Promise<any>;
    getUserLocationStats(userId: number, days?: number): Promise<any>;
    setTrustedLocation(userId: number, request: TrustLocationRequestDto): Promise<{
        message: string;
        data: any;
    }>;
    getCurrentIpLocation(request: Request): Promise<any>;
    healthCheck(): Promise<{
        status: string;
        timestamp: string;
        service: string;
    }>;
    private extractClientIP;
    private cleanAndValidateIP;
    private isValidIP;
    private getIpSourceName;
}

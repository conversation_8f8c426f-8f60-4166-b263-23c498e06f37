{"version": 3, "file": "unified-business.exception.js", "sourceRoot": "", "sources": ["../../../src/common/exceptions/unified-business.exception.ts"], "names": [], "mappings": ";;;AAAA,2CAA+C;AAC/C,oGAKgE;AAMhE,MAAa,wBAAyB,SAAQ,sBAAa;IACzC,SAAS,CAAY;IACrB,YAAY,CAAO;IACnB,YAAY,CAAS;IAErC,YACE,YAAoB,EACpB,OAAgB,EAChB,IAAU,EACV,OAAa;QAGb,MAAM,UAAU,GAAG,IAAA,2CAAiB,EAAC,YAAY,CAAC,CAAC;QAGnD,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,YAAY;YAClB,GAAG,EAAE,OAAO,IAAI,wBAAwB,CAAC,iBAAiB,CAAC,YAAY,CAAC;YACxE,IAAI,EAAE,IAAI,IAAI,IAAI;SACnB,CAAC;QAEF,KAAK,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAE5B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,SAAS,GAAG,IAAA,sCAAY,EAAC,YAAY,CAAC,CAAC;QAC5C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC;QAC5B,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACzC,CAAC;IAKO,MAAM,CAAC,iBAAiB,CAAC,IAAY;QAC3C,MAAM,QAAQ,GAA2B;YAEvC,CAAC,8CAAoB,CAAC,cAAc,CAAC,EAAE,OAAO;YAC9C,CAAC,8CAAoB,CAAC,mBAAmB,CAAC,EAAE,OAAO;YACnD,CAAC,8CAAoB,CAAC,aAAa,CAAC,EAAE,QAAQ;YAC9C,CAAC,8CAAoB,CAAC,WAAW,CAAC,EAAE,QAAQ;YAG5C,CAAC,8CAAoB,CAAC,aAAa,CAAC,EAAE,aAAa;YACnD,CAAC,8CAAoB,CAAC,aAAa,CAAC,EAAE,QAAQ;YAC9C,CAAC,8CAAoB,CAAC,YAAY,CAAC,EAAE,MAAM;YAC3C,CAAC,8CAAoB,CAAC,kBAAkB,CAAC,EAAE,MAAM;YACjD,CAAC,8CAAoB,CAAC,cAAc,CAAC,EAAE,QAAQ;YAG/C,CAAC,8CAAoB,CAAC,iBAAiB,CAAC,EAAE,MAAM;YAChD,CAAC,8CAAoB,CAAC,cAAc,CAAC,EAAE,OAAO;YAC9C,CAAC,8CAAoB,CAAC,uBAAuB,CAAC,EAAE,MAAM;YAGtD,CAAC,8CAAoB,CAAC,oBAAoB,CAAC,EAAE,MAAM;YACnD,CAAC,8CAAoB,CAAC,qBAAqB,CAAC,EAAE,QAAQ;YACtD,CAAC,8CAAoB,CAAC,eAAe,CAAC,EAAE,QAAQ;YAChD,CAAC,8CAAoB,CAAC,mBAAmB,CAAC,EAAE,QAAQ;YACpD,CAAC,8CAAoB,CAAC,gBAAgB,CAAC,EAAE,OAAO;YAGhD,CAAC,8CAAoB,CAAC,kBAAkB,CAAC,EAAE,OAAO;YAClD,CAAC,8CAAoB,CAAC,mBAAmB,CAAC,EAAE,cAAc;YAC1D,CAAC,8CAAoB,CAAC,sBAAsB,CAAC,EAAE,QAAQ;YACvD,CAAC,8CAAoB,CAAC,cAAc,CAAC,EAAE,OAAO;YAC9C,CAAC,8CAAoB,CAAC,WAAW,CAAC,EAAE,MAAM;SAC3C,CAAC;QAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC;IACpC,CAAC;IAOD,MAAM,CAAC,YAAY,CAAC,MAAwB,EAAE,OAAa;QACzD,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,cAAc,EACnC,OAAO,EACP,EAAE,MAAM,EAAE,EACV,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,UAAmB,EAAE,OAAa;QACzD,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,mBAAmB,EACxC,OAAO,EACP,EAAE,UAAU,EAAE,EACd,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,YAAY,CAAC,OAAa;QAC/B,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,aAAa,EAClC,aAAa,EACb,IAAI,EACJ,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,QAAiB,EAAE,MAAe,EAAE,OAAa;QACvE,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,iBAAiB,EACtC,MAAM,EACN,EAAE,QAAQ,EAAE,MAAM,EAAE,EACpB,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,mBAAmB,CAAC,cAAuB,EAAE,cAAuB,EAAE,OAAa;QACxF,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,oBAAoB,EACzC,MAAM,EACN,EAAE,cAAc,EAAE,cAAc,EAAE,EAClC,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,kBAAkB,CAAC,SAAkB,EAAE,OAAa;QACzD,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,mBAAmB,EACxC,QAAQ,EACR,EAAE,SAAS,EAAE,EACb,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,cAAc,CAAC,YAAqB,EAAE,UAAmB,EAAE,OAAa;QAC7E,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,eAAe,EACpC,QAAQ,EACR,EAAE,YAAY,EAAE,UAAU,EAAE,EAC5B,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,aAAsB,EAAE,OAAa;QAC5D,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,kBAAkB,EACvC,aAAa,EACb,EAAE,aAAa,EAAE,EACjB,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,iBAAiB,CAAC,UAAmB,EAAE,OAAa;QACzD,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,mBAAmB,EACxC,cAAc,EACd,EAAE,UAAU,EAAE,EACd,OAAO,CACR,CAAC;IACJ,CAAC;IAKD,MAAM,CAAC,oBAAoB,CAAC,WAAoB,EAAE,OAAa;QAC7D,OAAO,IAAI,wBAAwB,CACjC,8CAAoB,CAAC,sBAAsB,EAC3C,QAAQ,EACR,EAAE,WAAW,EAAE,EACf,OAAO,CACR,CAAC;IACJ,CAAC;CACF;AA/LD,4DA+LC;AAMD,MAAa,iBAAkB,SAAQ,wBAAwB;CAAG;AAAlE,8CAAkE;AAClE,MAAa,qBAAsB,SAAQ,wBAAwB;IACjE,YAAY,MAAwB,EAAE,OAAa;QACjD,KAAK,CAAC,8CAAoB,CAAC,cAAc,EAAE,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;IAC3E,CAAC;CACF;AAJD,sDAIC;AAED,MAAa,yBAA0B,SAAQ,wBAAwB;IACrE,YAAY,QAAiB,EAAE,MAAe,EAAE,OAAa;QAC3D,KAAK,CAAC,8CAAoB,CAAC,iBAAiB,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;IACvF,CAAC;CACF;AAJD,8DAIC;AAED,MAAa,qBAAsB,SAAQ,wBAAwB;IACjE,YAAY,OAAa;QACvB,KAAK,CAAC,8CAAoB,CAAC,aAAa,EAAE,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAC1E,CAAC;CACF;AAJD,sDAIC;AAED,MAAa,4BAA6B,SAAQ,wBAAwB;IACxE,YAAY,cAAuB,EAAE,cAAuB,EAAE,OAAa;QACzE,KAAK,CAAC,8CAAoB,CAAC,oBAAoB,EAAE,MAAM,EAAE,EAAE,cAAc,EAAE,cAAc,EAAE,EAAE,OAAO,CAAC,CAAC;IACxG,CAAC;CACF;AAJD,oEAIC;AAED,MAAa,2BAA4B,SAAQ,wBAAwB;IACvE,YAAY,SAAkB,EAAE,OAAa;QAC3C,KAAK,CAAC,8CAAoB,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,CAAC,CAAC;IACpF,CAAC;CACF;AAJD,kEAIC;AAED,MAAa,uBAAwB,SAAQ,wBAAwB;IACnE,YAAY,YAAqB,EAAE,UAAmB,EAAE,OAAa;QACnE,KAAK,CAAC,8CAAoB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,OAAO,CAAC,CAAC;IAC/F,CAAC;CACF;AAJD,0DAIC"}
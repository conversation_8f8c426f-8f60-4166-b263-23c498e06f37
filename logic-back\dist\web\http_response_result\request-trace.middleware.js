"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestTraceMiddleware = void 0;
const common_1 = require("@nestjs/common");
const request_trace_service_1 = require("./request-trace.service");
let RequestTraceMiddleware = class RequestTraceMiddleware {
    requestTraceService;
    constructor(requestTraceService) {
        this.requestTraceService = requestTraceService;
    }
    use(req, res, next) {
        this.requestTraceService.initializeFromRequest(req);
        const { requestId, traceId } = this.requestTraceService.getTraceInfo();
        res.setHeader('X-Request-ID', requestId);
        res.setHeader('X-Trace-ID', traceId);
        console.log(`[${traceId}] ${req.method} ${req.url} - Request started`);
        next();
    }
};
exports.RequestTraceMiddleware = RequestTraceMiddleware;
exports.RequestTraceMiddleware = RequestTraceMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [request_trace_service_1.RequestTraceService])
], RequestTraceMiddleware);
//# sourceMappingURL=request-trace.middleware.js.map
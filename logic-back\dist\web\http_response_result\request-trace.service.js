"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RequestTraceService = void 0;
const common_1 = require("@nestjs/common");
let RequestTraceService = class RequestTraceService {
    _requestId = null;
    _traceId = null;
    _startTime;
    _request = null;
    constructor() {
        this._startTime = Date.now();
    }
    initializeFromRequest(request) {
        this._request = request;
        const existingTraceId = request.headers['x-trace-id'];
        const existingRequestId = request.headers['x-request-id'];
        this._traceId = existingTraceId || this.generateTraceId();
        this._requestId = existingRequestId || this.generateRequestId();
        request.traceId = this._traceId;
        request.requestId = this._requestId;
    }
    getRequestId() {
        if (!this._requestId) {
            this._requestId = this.generateRequestId();
        }
        return this._requestId;
    }
    getTraceId() {
        if (!this._traceId) {
            this._traceId = this.generateTraceId();
        }
        return this._traceId;
    }
    getPath() {
        return this._request?.url || '';
    }
    getExecutionTime() {
        return Date.now() - this._startTime;
    }
    getTraceInfo() {
        return {
            requestId: this.getRequestId(),
            traceId: this.getTraceId(),
            path: this.getPath(),
            executionTime: this.getExecutionTime()
        };
    }
    generateRequestId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 11);
        return `req-${timestamp}-${random}`;
    }
    generateTraceId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 11);
        return `trace-${timestamp}-${random}`;
    }
    generateBusinessRequestId(prefix) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 11);
        return `${prefix}-${timestamp}-${random}`;
    }
    setCustomRequestId(requestId) {
        this._requestId = requestId;
        if (this._request) {
            this._request.requestId = requestId;
        }
    }
    setCustomTraceId(traceId) {
        this._traceId = traceId;
        if (this._request) {
            this._request.traceId = traceId;
        }
    }
    getClientIP() {
        if (!this._request)
            return '';
        return (this._request.headers['x-forwarded-for'] ||
            this._request.headers['x-real-ip'] ||
            this._request.connection?.remoteAddress ||
            this._request.socket?.remoteAddress ||
            '').split(',')[0].trim();
    }
    getUserAgent() {
        return this._request?.headers['user-agent'] || '';
    }
    getMethod() {
        return this._request?.method || '';
    }
    getRequestContext() {
        return {
            requestId: this.getRequestId(),
            traceId: this.getTraceId(),
            path: this.getPath(),
            method: this.getMethod(),
            clientIP: this.getClientIP(),
            userAgent: this.getUserAgent(),
            executionTime: this.getExecutionTime(),
            timestamp: new Date().toISOString()
        };
    }
    reset() {
        this._requestId = null;
        this._traceId = null;
        this._startTime = Date.now();
        this._request = null;
    }
};
exports.RequestTraceService = RequestTraceService;
exports.RequestTraceService = RequestTraceService = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.REQUEST }),
    __metadata("design:paramtypes", [])
], RequestTraceService);
//# sourceMappingURL=request-trace.service.js.map
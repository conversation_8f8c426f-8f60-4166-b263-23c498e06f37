"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpResponseResultModule = void 0;
const common_1 = require("@nestjs/common");
const http_response_result_service_1 = require("./http_response_result.service");
const http_response_result_controller_1 = require("./http_response_result.controller");
const logger_service_1 = require("../../common/logger/logger.service");
let HttpResponseResultModule = class HttpResponseResultModule {
};
exports.HttpResponseResultModule = HttpResponseResultModule;
exports.HttpResponseResultModule = HttpResponseResultModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        controllers: [http_response_result_controller_1.HttpResponseResultController],
        providers: [
            http_response_result_service_1.HttpResponseResultService,
            logger_service_1.LoggerService,
        ],
        exports: [http_response_result_service_1.HttpResponseResultService],
    })
], HttpResponseResultModule);
//# sourceMappingURL=http_response_result.module.js.map
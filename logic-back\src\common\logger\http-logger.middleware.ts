import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { LoggerService } from './logger.service';

@Injectable()
export class HttpLoggerMiddleware implements NestMiddleware {
  constructor(private readonly loggerService: LoggerService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const traceId = this.generateTraceId();

    // 将 traceId 添加到请求对象中，供后续使用
    (req as any).traceId = traceId;

    // 记录请求开始
    this.loggerService.debug(`[${traceId}] Incoming ${req.method} ${req.url}`, 'HTTP');

    // 监听响应结束事件
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;

      // 使用原有的简单日志记录（保持向后兼容）
      this.loggerService.logHttpRequest(req, res, responseTime);
    });

    next();
  }

  /**
   * 生成追踪ID
   */
  private generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * 增强的HTTP日志中间件
 * 记录详细的请求响应信息
 */
@Injectable()
export class EnhancedHttpLoggerMiddleware implements NestMiddleware {
  constructor(private readonly loggerService: LoggerService) {}

  use(req: Request, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const traceId = this.generateTraceId();

    // 将 traceId 添加到请求对象中
    (req as any).traceId = traceId;

    // 记录请求开始
    this.loggerService.debug(`[${traceId}] Enhanced Incoming ${req.method} ${req.url}`, 'HTTP_ENHANCED');

    // 捕获请求体和查询参数
    const requestBody = req.body;
    const requestQuery = req.query;

    // 捕获响应体
    let responseBody: any;
    const originalSend = res.send;
    const originalJson = res.json;

    // 重写 send 方法
    res.send = function(body: any) {
      responseBody = body;
      return originalSend.call(this, body);
    };

    // 重写 json 方法
    res.json = function(body: any) {
      responseBody = body;
      return originalJson.call(this, body);
    };

    // 监听响应结束事件
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;

      // 记录详细的请求响应日志
      this.loggerService.logHttpRequestResponse(
        req,
        res,
        responseTime,
        {
          requestBody,
          requestQuery,
          responseBody,
          traceId
        }
      );
    });

    next();
  }

  /**
   * 生成追踪ID
   */
  private generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }
}

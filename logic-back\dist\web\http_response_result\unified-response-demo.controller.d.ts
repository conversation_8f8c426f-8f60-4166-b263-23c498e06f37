import { Request } from 'express';
import { HttpResponseResultService } from './http_response_result.service';
import { BusinessModule } from '../../common/middleware/request-id.middleware';
export declare class UnifiedResponseDemoController {
    private readonly httpResponseService;
    constructor(httpResponseService: HttpResponseResultService);
    legacySuccess(): Promise<import("./http-response.interface").HttpResponse<{
        message: string;
    }>>;
    legacyError(): Promise<import("./http-response.interface").HttpResponse<{
        errorCode: string;
    }>>;
    unifiedSuccess(request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        message: string;
        features: string[];
    }>>;
    unifiedError(request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        errorCode: string;
    }>>;
    businessError(code: string, request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        demoParam: string;
    }>>;
    throwException(type: string): Promise<void>;
    enhancedDemo(body: any, request: Request): Promise<import("./http-response.interface").EnhancedHttpResponse<{
        processed: any;
        result: string;
    }> | import("./http-response.interface").EnhancedHttpResponse<null>>;
    businessRequestIdDemo(request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        current: {
            requestId: string;
            businessModule: any;
            modulePrefix: string;
            isFromDemo: boolean;
        };
        examples: {
            ipLocation: string;
            payment: string;
            user: string;
            auth: string;
            course: string;
            general: string;
        };
        pathInference: {
            '/api/v1/ip-location/query': BusinessModule;
            '/api/v1/payment/create': BusinessModule;
            '/api/v1/user/profile': BusinessModule;
            '/api/v1/auth/login': BusinessModule;
            '/api/v1/course/list': BusinessModule;
            '/api/v1/unknown/endpoint': BusinessModule;
        };
    }>>;
    formatComparison(format?: string): Promise<import("./http-response.interface").HttpResponse<{
        message: string;
        timestamp: Date;
        features: string[];
    }>>;
    private generateRequestId;
    private generateTraceId;
    private simulateAsyncOperation;
}

import { Request } from 'express';
import { HttpResponseResultService } from './http_response_result.service';
export declare class UnifiedResponseDemoController {
    private readonly httpResponseService;
    constructor(httpResponseService: HttpResponseResultService);
    legacySuccess(): Promise<import("./http-response.interface").HttpResponse<{
        message: string;
    }>>;
    legacyError(): Promise<import("./http-response.interface").HttpResponse<{
        errorCode: string;
    }>>;
    unifiedSuccess(request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        message: string;
        features: string[];
    }>>;
    unifiedError(request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        errorCode: string;
    }>>;
    businessError(code: string, request: Request): Promise<import("./http-response.interface").UnifiedResponse<{
        demoParam: string;
    }>>;
    throwException(type: string): Promise<void>;
    enhancedDemo(body: any, request: Request): Promise<import("./http-response.interface").EnhancedHttpResponse<{
        processed: any;
        result: string;
    }> | import("./http-response.interface").EnhancedHttpResponse<null>>;
    formatComparison(format?: string): Promise<import("./http-response.interface").HttpResponse<{
        message: string;
        timestamp: Date;
        features: string[];
    }>>;
    private generateRequestId;
    private generateTraceId;
    private simulateAsyncOperation;
}

{"version": 3, "file": "http-response.interface.js", "sourceRoot": "", "sources": ["../../../src/web/http_response_result/http-response.interface.ts"], "names": [], "mappings": ";;;AAyMA,8CAEC;AAKD,oCAYC;AAnIY,QAAA,YAAY,GAAG,GAAG,CAAC;AAKnB,QAAA,UAAU,GAAG,GAAG,CAAC;AAKjB,QAAA,gBAAgB,GAAG,GAAG,CAAC;AAKvB,QAAA,iBAAiB,GAAG,GAAG,CAAC;AAKxB,QAAA,cAAc,GAAG,GAAG,CAAC;AAKrB,QAAA,cAAc,GAAG,GAAG,CAAC;AAOrB,QAAA,aAAa,GAAG;IAC3B,EAAE,EAAE,GAAG;IACP,OAAO,EAAE,GAAG;IACZ,QAAQ,EAAE,GAAG;IACb,eAAe,EAAE,GAAG;CACZ,CAAC;AAKE,QAAA,oBAAoB,GAAG;IAElC,cAAc,EAAE,IAAI;IACpB,mBAAmB,EAAE,IAAI;IACzB,aAAa,EAAE,IAAI;IACnB,WAAW,EAAE,IAAI;IAGjB,aAAa,EAAE,IAAI;IACnB,aAAa,EAAE,IAAI;IACnB,YAAY,EAAE,IAAI;IAClB,kBAAkB,EAAE,IAAI;IACxB,cAAc,EAAE,IAAI;IAGpB,iBAAiB,EAAE,IAAI;IACvB,cAAc,EAAE,IAAI;IACpB,uBAAuB,EAAE,IAAI;IAG7B,oBAAoB,EAAE,IAAI;IAC1B,qBAAqB,EAAE,IAAI;IAC3B,eAAe,EAAE,IAAI;IACrB,mBAAmB,EAAE,IAAI;IACzB,gBAAgB,EAAE,IAAI;IAGtB,kBAAkB,EAAE,IAAI;IACxB,mBAAmB,EAAE,IAAI;IACzB,sBAAsB,EAAE,IAAI;IAC5B,cAAc,EAAE,IAAI;IACpB,WAAW,EAAE,IAAI;CACT,CAAC;AAKX,IAAY,SASX;AATD,WAAY,SAAS;IACnB,yCAA4B,CAAA;IAC5B,6CAAgC,CAAA;IAChC,iDAAoC,CAAA;IACpC,yDAA4C,CAAA;IAC5C,uDAA0C,CAAA;IAC1C,2CAA8B,CAAA;IAC9B,6CAAgC,CAAA;IAChC,4DAA+C,CAAA;AACjD,CAAC,EATW,SAAS,yBAAT,SAAS,QASpB;AAKY,QAAA,mBAAmB,GAA2B;IAEzD,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAGtC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAC9E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IAGhD,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAC1C,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IACrD,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAC/B,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IACrD,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;CACtD,CAAC;AAKF,SAAgB,iBAAiB,CAAC,YAAoB;IACpD,OAAO,2BAAmB,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC;AAClD,CAAC;AAKD,SAAgB,YAAY,CAAC,IAAY;IACvC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,cAAc,CAAC;IACjE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,oBAAoB,CAAC;IACvE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,mBAAmB,CAAC;IACtE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,cAAc,CAAC;IACjE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI;QAAE,OAAO,SAAS,CAAC,YAAY,CAAC;IAC/D,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC,gBAAgB,CAAC;IACpE,IAAI,IAAI,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC,oBAAoB,CAAC;IACxD,IAAI,IAAI,KAAK,GAAG;QAAE,OAAO,SAAS,CAAC,mBAAmB,CAAC;IACvD,IAAI,IAAI,IAAI,GAAG;QAAE,OAAO,SAAS,CAAC,YAAY,CAAC;IAE/C,OAAO,SAAS,CAAC,YAAY,CAAC;AAChC,CAAC"}
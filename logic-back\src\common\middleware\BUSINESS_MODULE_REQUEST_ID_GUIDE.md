# 业务模块RequestID设计指南

## 🎯 设计目标

为不同业务模块提供专用的requestId前缀，便于日志分析、问题定位和监控统计。

## 📊 业务模块前缀定义

### **支持的业务模块**
```typescript
export enum BusinessModule {
  IP_LOCATION = 'ip-loc',    // IP地理位置模块
  PAYMENT = 'payment',       // 支付模块
  USER = 'user',            // 用户模块
  AUTH = 'auth',            // 认证模块
  COURSE = 'course',        // 课程模块
  TASK = 'task',            // 任务模块
  PACKAGE = 'package',      // 套餐模块
  SCRATCH = 'scratch',      // Scratch编程模块
  TPS = 'tps',             // TPS模块
  GENERAL = 'req'          // 通用/未知模块
}
```

### **RequestID格式示例**
```
ip-loc-1754109415502-whofn26ff    // IP地理位置模块
payment-1754109415502-abc123def   // 支付模块
user-1754109415502-xyz789ghi      // 用户模块
auth-1754109415502-mno456pqr      // 认证模块
course-1754109415502-stu789vwx    // 课程模块
req-1754109415502-general123      // 通用模块
```

## 🚀 自动路径推断

### **路径模式匹配**
```typescript
static inferBusinessModule(path: string): BusinessModule {
  const cleanPath = path.split('?')[0].toLowerCase();

  if (cleanPath.includes('/ip-location')) return BusinessModule.IP_LOCATION;
  if (cleanPath.includes('/payment')) return BusinessModule.PAYMENT;
  if (cleanPath.includes('/user')) return BusinessModule.USER;
  if (cleanPath.includes('/auth') || cleanPath.includes('/login')) return BusinessModule.AUTH;
  if (cleanPath.includes('/course')) return BusinessModule.COURSE;
  if (cleanPath.includes('/task')) return BusinessModule.TASK;
  if (cleanPath.includes('/package')) return BusinessModule.PACKAGE;
  if (cleanPath.includes('/scratch')) return BusinessModule.SCRATCH;
  if (cleanPath.includes('/tps')) return BusinessModule.TPS;

  return BusinessModule.GENERAL;
}
```

### **路径推断示例**
| 请求路径 | 推断模块 | RequestID前缀 |
|---------|---------|--------------|
| `/api/v1/ip-location/query` | IP_LOCATION | `ip-loc-` |
| `/api/v1/payment/create` | PAYMENT | `payment-` |
| `/api/v1/user/profile` | USER | `user-` |
| `/api/v1/auth/login` | AUTH | `auth-` |
| `/api/v1/course/list` | COURSE | `course-` |
| `/api/v1/unknown/endpoint` | GENERAL | `req-` |

## 💻 使用方式

### **1. 自动推断（推荐）**
```typescript
// 请求ID中间件自动根据路径推断业务模块
@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 自动推断业务模块
    const businessModule = RequestIdUtils.inferBusinessModule(req.url);
    const requestId = RequestIdUtils.generateBusinessRequestId(businessModule);
    
    (req as any).requestId = requestId;
    (req as any).businessModule = businessModule;
    
    res.setHeader('X-Request-ID', requestId);
    res.setHeader('X-Business-Module', businessModule);
    
    next();
  }
}
```

### **2. 手动指定业务模块**
```typescript
// 在业务服务中手动生成特定模块的requestId
@Injectable()
export class IpLocationFacadeService {
  private generateRequestId(): string {
    return RequestIdUtils.generateBusinessRequestId(BusinessModule.IP_LOCATION);
  }
}
```

### **3. 从请求中获取**
```typescript
// 在控制器中获取请求的业务requestId
@Controller('api/v1/ip-location')
export class IpLocationController {
  @Get('current')
  async getCurrentIpLocation(@Req() request: Request) {
    const requestId = RequestIdUtils.getBusinessRequestId(request, BusinessModule.IP_LOCATION);
    const businessModule = (request as any).businessModule;
    
    // 使用业务requestId...
  }
}
```

## 🔧 工具方法

### **RequestIdUtils提供的方法**
```typescript
// 生成业务模块requestId
RequestIdUtils.generateBusinessRequestId(BusinessModule.IP_LOCATION)
// 返回: "ip-loc-1754109415502-whofn26ff"

// 从请求中获取业务requestId
RequestIdUtils.getBusinessRequestId(request, BusinessModule.IP_LOCATION)

// 推断业务模块
RequestIdUtils.inferBusinessModule('/api/v1/ip-location/query')
// 返回: BusinessModule.IP_LOCATION

// 检查requestId是否属于指定模块
RequestIdUtils.isFromModule('ip-loc-123-abc', BusinessModule.IP_LOCATION)
// 返回: true

// 提取模块前缀
RequestIdUtils.extractModulePrefix('ip-loc-123-abc')
// 返回: "ip-loc"
```

## 📊 响应示例

### **IP地理位置模块**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {...},
  "success": true,
  "timestamp": "2025-08-02T04:36:55.502Z",
  "meta": {
    "requestId": "ip-loc-1754109415502-whofn26ff"
  }
}
```

### **支付模块**
```json
{
  "code": 200,
  "msg": "支付成功",
  "data": {...},
  "success": true,
  "timestamp": "2025-08-02T04:36:55.502Z",
  "trace": {
    "requestId": "payment-1754109415502-abc123def"
  }
}
```

### **用户模块**
```json
{
  "code": 200,
  "msg": "用户信息获取成功",
  "data": {...},
  "success": true,
  "timestamp": "2025-08-02T04:36:55.502Z",
  "trace": {
    "requestId": "user-1754109415502-xyz789ghi"
  }
}
```

## 🎯 业务价值

### **1. 日志分析**
```bash
# 查看IP地理位置模块的所有请求
grep "ip-loc-" application.log

# 查看支付模块的错误日志
grep "payment-.*ERROR" application.log

# 统计各模块的请求量
grep -o "^[a-z-]*" application.log | sort | uniq -c
```

### **2. 监控统计**
```typescript
// 根据requestId前缀进行业务监控
const modulePrefix = RequestIdUtils.extractModulePrefix(requestId);
metrics.increment(`api.requests.${modulePrefix}`);
```

### **3. 问题定位**
```typescript
// 快速识别问题所属的业务模块
if (RequestIdUtils.isFromModule(requestId, BusinessModule.IP_LOCATION)) {
  // 这是IP地理位置模块的问题
  notifyIpLocationTeam(error);
} else if (RequestIdUtils.isFromModule(requestId, BusinessModule.PAYMENT)) {
  // 这是支付模块的问题
  notifyPaymentTeam(error);
}
```

## 🔍 测试验证

### **演示接口**
```bash
# 测试业务模块requestId演示
curl -X GET "http://localhost:8003/api/v1/demo/business-request-id"

# 测试IP地理位置模块
curl -X GET "http://localhost:8003/api/v1/ip-location/current"

# 测试支付模块（如果有）
curl -X GET "http://localhost:8003/api/v1/payment/status"

# 测试用户模块（如果有）
curl -X GET "http://localhost:8003/api/v1/user/profile"
```

### **响应头检查**
```bash
# 检查响应头中的业务模块信息
curl -v -X GET "http://localhost:8003/api/v1/ip-location/current"

# 应该看到：
# X-Request-ID: ip-loc-1754109415502-whofn26ff
# X-Business-Module: ip-loc
```

## 🎉 总结

通过业务模块requestId设计：

1. **🎯 精确定位**：每个业务模块都有专用的requestId前缀
2. **🔍 便于分析**：可以快速过滤和统计特定模块的日志
3. **📊 监控友好**：支持按业务模块进行监控和告警
4. **🚀 自动推断**：根据请求路径自动推断业务模块
5. **🛠️ 工具丰富**：提供完整的工具方法支持

这样既保持了requestId的唯一性，又增强了业务可观测性！🐱

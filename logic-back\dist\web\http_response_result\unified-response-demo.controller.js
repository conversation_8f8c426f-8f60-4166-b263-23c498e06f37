"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnifiedResponseDemoController = void 0;
const common_1 = require("@nestjs/common");
const http_response_result_service_1 = require("./http_response_result.service");
const unified_business_exception_1 = require("../../common/exceptions/unified-business.exception");
const http_response_interface_1 = require("./http-response.interface");
const request_id_middleware_1 = require("../../common/middleware/request-id.middleware");
const swagger_1 = require("@nestjs/swagger");
let UnifiedResponseDemoController = class UnifiedResponseDemoController {
    httpResponseService;
    constructor(httpResponseService) {
        this.httpResponseService = httpResponseService;
    }
    async legacySuccess() {
        return this.httpResponseService.success({ message: 'This is legacy format' }, '操作成功');
    }
    async legacyError() {
        return this.httpResponseService.error('这是原有的错误格式', { errorCode: 'DEMO_ERROR' }, 400);
    }
    async unifiedSuccess(request) {
        const requestId = this.generateRequestId();
        const traceId = request.traceId || this.generateTraceId();
        return this.httpResponseService.unifiedSuccess({
            message: 'This is unified format',
            features: ['success flag', 'timestamp', 'trace info']
        }, '统一格式操作成功', 200, {
            requestId,
            traceId,
            path: request.url,
            executionTime: 25,
            fromCache: false
        });
    }
    async unifiedError(request) {
        const requestId = this.generateRequestId();
        const traceId = request.traceId || this.generateTraceId();
        return this.httpResponseService.unifiedError('这是统一格式的错误响应', { errorCode: 'DEMO_ERROR' }, 400, {
            requestId,
            traceId,
            path: request.url,
            errorDetails: { reason: 'This is a demo error' },
            executionTime: 15
        });
    }
    async businessError(code, request) {
        const requestId = this.generateRequestId();
        const businessCode = parseInt(code);
        return this.httpResponseService.businessError(businessCode, undefined, { demoParam: code }, {
            requestId,
            path: request.url,
            errorDetails: {
                requestedCode: businessCode,
                availableCodes: Object.values(http_response_interface_1.BUSINESS_ERROR_CODES)
            }
        });
    }
    async throwException(type) {
        switch (type) {
            case 'user-not-found':
                throw unified_business_exception_1.UnifiedBusinessException.userNotFound(12345, {
                    searchMethod: 'findById',
                    searchedAt: new Date()
                });
            case 'permission-denied':
                throw unified_business_exception_1.UnifiedBusinessException.permissionDenied('demo-resource', 'read', { requiredRole: 'admin', userRole: 'user' });
            case 'token-expired':
                throw unified_business_exception_1.UnifiedBusinessException.tokenExpired({
                    expiredAt: new Date(),
                    tokenType: 'access_token'
                });
            case 'insufficient-balance':
                throw unified_business_exception_1.UnifiedBusinessException.insufficientBalance(100, 500, { currency: 'CNY', accountId: 'demo-account' });
            case 'duplicate-operation':
                throw unified_business_exception_1.UnifiedBusinessException.duplicateOperation('create-user', { previousOperationId: 'op-123', interval: '5s' });
            case 'rate-limit':
                throw unified_business_exception_1.UnifiedBusinessException.rateLimitExceeded(60, { limit: 100, window: '1h', current: 101 });
            default:
                throw new unified_business_exception_1.UnifiedBusinessException(5000, '未知的异常类型', { requestedType: type }, { availableTypes: ['user-not-found', 'permission-denied', 'token-expired'] });
        }
    }
    async enhancedDemo(body, request) {
        const startTime = Date.now();
        const requestId = this.generateRequestId();
        try {
            await this.simulateAsyncOperation();
            return this.httpResponseService.enhancedSuccess({
                processed: body,
                result: 'Enhanced response with unified features'
            }, '增强响应处理成功', 200, {
                executionTime: Date.now() - startTime,
                fromCache: false,
                includeTimestamp: true,
                meta: {
                    requestId,
                    version: 'v2.0.0',
                    features: ['enhanced', 'unified', 'backward-compatible']
                },
                logContext: {
                    module: 'Demo',
                    operation: 'enhancedDemo',
                    requestData: body
                }
            });
        }
        catch (error) {
            return this.httpResponseService.enhancedError('增强响应处理失败', null, 500, {
                executionTime: Date.now() - startTime,
                includeTimestamp: true,
                meta: { requestId },
                logContext: {
                    module: 'Demo',
                    operation: 'enhancedDemo',
                    requestData: body,
                    error: error
                }
            });
        }
    }
    async businessRequestIdDemo(request) {
        const requestId = request_id_middleware_1.RequestIdUtils.getRequestId(request);
        const businessModule = request.businessModule;
        const modulePrefix = request_id_middleware_1.RequestIdUtils.extractModulePrefix(requestId);
        const examples = {
            current: {
                requestId,
                businessModule,
                modulePrefix,
                isFromDemo: request_id_middleware_1.RequestIdUtils.isFromModule(requestId, request_id_middleware_1.BusinessModule.GENERAL)
            },
            examples: {
                ipLocation: request_id_middleware_1.RequestIdUtils.generateBusinessRequestId(request_id_middleware_1.BusinessModule.IP_LOCATION),
                payment: request_id_middleware_1.RequestIdUtils.generateBusinessRequestId(request_id_middleware_1.BusinessModule.PAYMENT),
                user: request_id_middleware_1.RequestIdUtils.generateBusinessRequestId(request_id_middleware_1.BusinessModule.USER),
                auth: request_id_middleware_1.RequestIdUtils.generateBusinessRequestId(request_id_middleware_1.BusinessModule.AUTH),
                course: request_id_middleware_1.RequestIdUtils.generateBusinessRequestId(request_id_middleware_1.BusinessModule.COURSE),
                general: request_id_middleware_1.RequestIdUtils.generateBusinessRequestId(request_id_middleware_1.BusinessModule.GENERAL)
            },
            pathInference: {
                '/api/v1/ip-location/query': request_id_middleware_1.RequestIdUtils.inferBusinessModule('/api/v1/ip-location/query'),
                '/api/v1/payment/create': request_id_middleware_1.RequestIdUtils.inferBusinessModule('/api/v1/payment/create'),
                '/api/v1/user/profile': request_id_middleware_1.RequestIdUtils.inferBusinessModule('/api/v1/user/profile'),
                '/api/v1/auth/login': request_id_middleware_1.RequestIdUtils.inferBusinessModule('/api/v1/auth/login'),
                '/api/v1/course/list': request_id_middleware_1.RequestIdUtils.inferBusinessModule('/api/v1/course/list'),
                '/api/v1/unknown/endpoint': request_id_middleware_1.RequestIdUtils.inferBusinessModule('/api/v1/unknown/endpoint')
            }
        };
        return this.httpResponseService.unifiedSuccess(examples, '业务模块requestId演示', 200, {
            requestId,
            path: request.url
        });
    }
    async formatComparison(format = 'unified') {
        const data = {
            message: 'Response format comparison',
            timestamp: new Date(),
            features: ['flexible', 'compatible', 'enhanced']
        };
        switch (format) {
            case 'legacy':
                return this.httpResponseService.success(data, '原有格式');
            case 'enhanced':
                return this.httpResponseService.enhancedSuccess(data, '增强格式', 200, {
                    executionTime: 10,
                    fromCache: false,
                    meta: { version: 'v2.0.0' }
                });
            case 'unified':
            default:
                return this.httpResponseService.unifiedSuccess(data, '统一格式', 200, {
                    requestId: this.generateRequestId(),
                    path: '/api/v1/demo/format-comparison',
                    executionTime: 10
                });
        }
    }
    generateRequestId() {
        return `demo-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    async simulateAsyncOperation() {
        return new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    }
};
exports.UnifiedResponseDemoController = UnifiedResponseDemoController;
__decorate([
    (0, common_1.Get)('legacy-success'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "legacySuccess", null);
__decorate([
    (0, common_1.Get)('legacy-error'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "legacyError", null);
__decorate([
    (0, common_1.Get)('unified-success'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "unifiedSuccess", null);
__decorate([
    (0, common_1.Get)('unified-error'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "unifiedError", null);
__decorate([
    (0, common_1.Get)('business-error/:code'),
    __param(0, (0, common_1.Param)('code')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "businessError", null);
__decorate([
    (0, common_1.Get)('throw-exception/:type'),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "throwException", null);
__decorate([
    (0, common_1.Post)('enhanced-demo'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "enhancedDemo", null);
__decorate([
    (0, common_1.Get)('business-request-id'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "businessRequestIdDemo", null);
__decorate([
    (0, common_1.Get)('format-comparison'),
    __param(0, (0, common_1.Query)('format')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UnifiedResponseDemoController.prototype, "formatComparison", null);
exports.UnifiedResponseDemoController = UnifiedResponseDemoController = __decorate([
    (0, swagger_1.ApiTags)('web/统一响应格式演示(unified-response-demo)'),
    (0, common_1.Controller)('api/v1/demo'),
    __metadata("design:paramtypes", [http_response_result_service_1.HttpResponseResultService])
], UnifiedResponseDemoController);
//# sourceMappingURL=unified-response-demo.controller.js.map
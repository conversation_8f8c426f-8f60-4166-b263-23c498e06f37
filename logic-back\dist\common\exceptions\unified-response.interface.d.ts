export interface UnifiedResponse<T = any> {
    code: number;
    msg: string;
    data: T | null;
    success: boolean;
    timestamp: string;
    trace?: {
        requestId?: string;
        traceId?: string;
        path?: string;
    };
    error?: {
        type: string;
        details?: any;
        stack?: string;
    };
}
export declare const SUCCESS_CODES: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly ACCEPTED: 202;
    readonly PARTIAL_SUCCESS: 206;
};
export declare const CLIENT_ERROR_CODES: {
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly METHOD_NOT_ALLOWED: 405;
    readonly CONFLICT: 409;
    readonly VALIDATION_ERROR: 422;
    readonly TOO_MANY_REQUESTS: 429;
};
export declare const SERVER_ERROR_CODES: {
    readonly INTERNAL_ERROR: 500;
    readonly NOT_IMPLEMENTED: 501;
    readonly BAD_GATEWAY: 502;
    readonly SERVICE_UNAVAILABLE: 503;
    readonly GATEWAY_TIMEOUT: 504;
};
export declare const BUSINESS_ERROR_CODES: {
    readonly USER_NOT_FOUND: 1001;
    readonly USER_ALREADY_EXISTS: 1002;
    readonly USER_DISABLED: 1003;
    readonly USER_LOCKED: 1004;
    readonly TOKEN_EXPIRED: 2001;
    readonly TOKEN_INVALID: 2002;
    readonly LOGIN_FAILED: 2003;
    readonly PASSWORD_INCORRECT: 2004;
    readonly ACCOUNT_LOCKED: 2005;
    readonly PERMISSION_DENIED: 3001;
    readonly ROLE_NOT_FOUND: 3002;
    readonly INSUFFICIENT_PRIVILEGES: 3003;
    readonly INSUFFICIENT_BALANCE: 4001;
    readonly OPERATION_NOT_ALLOWED: 4002;
    readonly RESOURCE_LOCKED: 4003;
    readonly DUPLICATE_OPERATION: 4004;
    readonly RESOURCE_EXPIRED: 4005;
    readonly SYSTEM_MAINTENANCE: 5001;
    readonly RATE_LIMIT_EXCEEDED: 5002;
    readonly EXTERNAL_SERVICE_ERROR: 5003;
    readonly DATABASE_ERROR: 5004;
    readonly CACHE_ERROR: 5005;
};
export declare enum ErrorType {
    SYSTEM_ERROR = "SystemError",
    BUSINESS_ERROR = "BusinessError",
    VALIDATION_ERROR = "ValidationError",
    AUTHENTICATION_ERROR = "AuthenticationError",
    AUTHORIZATION_ERROR = "AuthorizationError",
    NETWORK_ERROR = "NetworkError",
    DATABASE_ERROR = "DatabaseError",
    EXTERNAL_SERVICE_ERROR = "ExternalServiceError"
}
export interface ErrorMessage {
    userMessage: string;
    developerMessage: string;
    errorCode: string;
    moreInfo?: string;
}
export declare const ERROR_MESSAGES: Record<string, ErrorMessage>;
export declare const CODE_TO_HTTP_STATUS: Record<number, number>;
export declare function getHttpStatusCode(businessCode: number): number;
export declare function getErrorType(code: number): ErrorType;

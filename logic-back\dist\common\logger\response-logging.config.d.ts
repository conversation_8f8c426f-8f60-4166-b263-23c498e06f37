export interface ResponseLoggingConfig {
    enableDetailedLogging: boolean;
    logRequestBody: boolean;
    logResponseBody: boolean;
    slowRequestThreshold: number;
    sensitiveFields: string[];
    excludePaths: string[];
    detailedLogPaths: string[];
    maxRequestBodySize: number;
    maxResponseBodySize: number;
}
export declare const DEFAULT_RESPONSE_LOGGING_CONFIG: ResponseLoggingConfig;
export declare enum ResponseLogLevel {
    BASIC = "basic",
    DETAILED = "detailed",
    DEBUG = "debug"
}
export interface LogContext {
    module: string;
    operation: string;
    userId?: string;
    traceId?: string;
    requestData?: any;
    error?: Error;
    extra?: Record<string, any>;
}
export declare class ResponseLoggingUtils {
    static shouldExcludePath(path: string, config: ResponseLoggingConfig): boolean;
    static shouldDetailedLog(path: string, config: ResponseLoggingConfig): boolean;
    static isSlowRequest(responseTime: number, config: ResponseLoggingConfig): boolean;
    static truncateData(data: any, maxSize: number, type: 'request' | 'response'): any;
    static generateLogTags(req: any, res: any, responseTime: number, config: ResponseLoggingConfig): string[];
    static formatLogMessage(req: any, res: any, responseTime: number, traceId?: string): string;
}
export declare class DataSanitizer {
    static sanitize(data: any, sensitiveFields: string[]): any;
    private static isSensitiveField;
    private static maskValue;
}

import { HttpException } from '@nestjs/common';
import { 
  BUSINESS_ERROR_CODES, 
  ErrorType, 
  getHttpStatusCode, 
  getErrorType 
} from '../../web/http_response_result/http-response.interface';

/**
 * 统一业务异常类
 * 向后兼容现有异常处理，同时支持新的统一响应格式
 */
export class UnifiedBusinessException extends HttpException {
  public readonly errorType: ErrorType;
  public readonly errorDetails?: any;
  public readonly businessCode: number;

  constructor(
    businessCode: number,
    message?: string,
    data?: any,
    details?: any
  ) {
    // 获取对应的HTTP状态码
    const httpStatus = getHttpStatusCode(businessCode);
    
    // 构建响应体（保持向后兼容的格式）
    const response = {
      code: businessCode,
      msg: message || UnifiedBusinessException.getDefaultMessage(businessCode),
      data: data || null
    };

    super(response, httpStatus);

    this.businessCode = businessCode;
    this.errorType = getErrorType(businessCode);
    this.errorDetails = details;
    this.name = 'UnifiedBusinessException';
  }

  /**
   * 获取默认错误消息
   */
  private static getDefaultMessage(code: number): string {
    const messages: Record<number, string> = {
      // 用户相关
      [BUSINESS_ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
      [BUSINESS_ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
      [BUSINESS_ERROR_CODES.USER_DISABLED]: '用户已被禁用',
      [BUSINESS_ERROR_CODES.USER_LOCKED]: '用户已被锁定',
      
      // 认证相关
      [BUSINESS_ERROR_CODES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
      [BUSINESS_ERROR_CODES.TOKEN_INVALID]: '登录凭证无效',
      [BUSINESS_ERROR_CODES.LOGIN_FAILED]: '登录失败',
      [BUSINESS_ERROR_CODES.PASSWORD_INCORRECT]: '密码错误',
      [BUSINESS_ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定',
      
      // 权限相关
      [BUSINESS_ERROR_CODES.PERMISSION_DENIED]: '权限不足',
      [BUSINESS_ERROR_CODES.ROLE_NOT_FOUND]: '角色不存在',
      [BUSINESS_ERROR_CODES.INSUFFICIENT_PRIVILEGES]: '权限不足',
      
      // 业务逻辑相关
      [BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
      [BUSINESS_ERROR_CODES.OPERATION_NOT_ALLOWED]: '操作不被允许',
      [BUSINESS_ERROR_CODES.RESOURCE_LOCKED]: '资源已被锁定',
      [BUSINESS_ERROR_CODES.DUPLICATE_OPERATION]: '请勿重复操作',
      [BUSINESS_ERROR_CODES.RESOURCE_EXPIRED]: '资源已过期',
      
      // 系统相关
      [BUSINESS_ERROR_CODES.SYSTEM_MAINTENANCE]: '系统维护中',
      [BUSINESS_ERROR_CODES.RATE_LIMIT_EXCEEDED]: '操作过于频繁，请稍后重试',
      [BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR]: '外部服务异常',
      [BUSINESS_ERROR_CODES.DATABASE_ERROR]: '数据库异常',
      [BUSINESS_ERROR_CODES.CACHE_ERROR]: '缓存异常',
    };

    return messages[code] || '业务处理异常';
  }

  // ==================== 静态工厂方法 ====================

  /**
   * 用户不存在异常
   */
  static userNotFound(userId?: number | string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.USER_NOT_FOUND,
      '用户不存在',
      { userId },
      details
    );
  }

  /**
   * 用户已存在异常
   */
  static userAlreadyExists(identifier?: string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.USER_ALREADY_EXISTS,
      '用户已存在',
      { identifier },
      details
    );
  }

  /**
   * Token过期异常
   */
  static tokenExpired(details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.TOKEN_EXPIRED,
      '登录已过期，请重新登录',
      null,
      details
    );
  }

  /**
   * 权限不足异常
   */
  static permissionDenied(resource?: string, action?: string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.PERMISSION_DENIED,
      '权限不足',
      { resource, action },
      details
    );
  }

  /**
   * 余额不足异常
   */
  static insufficientBalance(currentBalance?: number, requiredAmount?: number, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE,
      '余额不足',
      { currentBalance, requiredAmount },
      details
    );
  }

  /**
   * 重复操作异常
   */
  static duplicateOperation(operation?: string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.DUPLICATE_OPERATION,
      '请勿重复操作',
      { operation },
      details
    );
  }

  /**
   * 资源锁定异常
   */
  static resourceLocked(resourceType?: string, resourceId?: string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.RESOURCE_LOCKED,
      '资源已被锁定',
      { resourceType, resourceId },
      details
    );
  }

  /**
   * 系统维护异常
   */
  static systemMaintenance(estimatedTime?: string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.SYSTEM_MAINTENANCE,
      '系统维护中，请稍后重试',
      { estimatedTime },
      details
    );
  }

  /**
   * 频率限制异常
   */
  static rateLimitExceeded(retryAfter?: number, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.RATE_LIMIT_EXCEEDED,
      '操作过于频繁，请稍后重试',
      { retryAfter },
      details
    );
  }

  /**
   * 外部服务异常
   */
  static externalServiceError(serviceName?: string, details?: any): UnifiedBusinessException {
    return new UnifiedBusinessException(
      BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR,
      '外部服务异常',
      { serviceName },
      details
    );
  }
}

/**
 * 向后兼容的异常类别名
 * 保持与现有代码的兼容性
 */
export class BusinessException extends UnifiedBusinessException {}
export class UserNotFoundException extends UnifiedBusinessException {
  constructor(userId?: number | string, details?: any) {
    super(BUSINESS_ERROR_CODES.USER_NOT_FOUND, '用户不存在', { userId }, details);
  }
}

export class PermissionDeniedException extends UnifiedBusinessException {
  constructor(resource?: string, action?: string, details?: any) {
    super(BUSINESS_ERROR_CODES.PERMISSION_DENIED, '权限不足', { resource, action }, details);
  }
}

export class TokenExpiredException extends UnifiedBusinessException {
  constructor(details?: any) {
    super(BUSINESS_ERROR_CODES.TOKEN_EXPIRED, '登录已过期，请重新登录', null, details);
  }
}

export class InsufficientBalanceException extends UnifiedBusinessException {
  constructor(currentBalance?: number, requiredAmount?: number, details?: any) {
    super(BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE, '余额不足', { currentBalance, requiredAmount }, details);
  }
}

export class DuplicateOperationException extends UnifiedBusinessException {
  constructor(operation?: string, details?: any) {
    super(BUSINESS_ERROR_CODES.DUPLICATE_OPERATION, '请勿重复操作', { operation }, details);
  }
}

export class ResourceLockedException extends UnifiedBusinessException {
  constructor(resourceType?: string, resourceId?: string, details?: any) {
    super(BUSINESS_ERROR_CODES.RESOURCE_LOCKED, '资源已被锁定', { resourceType, resourceId }, details);
  }
}

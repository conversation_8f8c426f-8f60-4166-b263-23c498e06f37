import { Global, Module } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import { LoggerService } from './logger.service';
import { ConsoleOverrideService } from './console-override.service';
import { createLoggerConfig } from './logger.config';

/**
 * 全局Logger模块
 * 提供全局的日志服务，包括HTTP请求响应日志记录
 */
@Global()
@Module({
  imports: [
    WinstonModule.forRoot(createLoggerConfig())
  ],
  controllers: [],
  providers: [LoggerService, ConsoleOverrideService],
  exports: [LoggerService, ConsoleOverrideService, WinstonModule],
})
export class LoggerModule {}

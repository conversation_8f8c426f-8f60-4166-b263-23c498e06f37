"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataSanitizer = exports.ResponseLoggingUtils = exports.ResponseLogLevel = exports.DEFAULT_RESPONSE_LOGGING_CONFIG = void 0;
exports.DEFAULT_RESPONSE_LOGGING_CONFIG = {
    enableDetailedLogging: true,
    logRequestBody: true,
    logResponseBody: true,
    slowRequestThreshold: 1000,
    sensitiveFields: [
        'password', 'oldPassword', 'newPassword',
        'token', 'accessToken', 'refreshToken',
        'cardNumber', 'cvv', 'bankAccount', 'qrCode',
        'secret', 'key', 'privateKey', 'apiKey'
    ],
    excludePaths: [
        '/health',
        '/metrics',
        '/favicon.ico',
        '/weixin/message'
    ],
    detailedLogPaths: [
        '/api/v1/ip-location',
        '/api/v1/payment',
        '/api/v1/auth',
        '/api/v1/user'
    ],
    maxRequestBodySize: 10 * 1024,
    maxResponseBodySize: 50 * 1024
};
var ResponseLogLevel;
(function (ResponseLogLevel) {
    ResponseLogLevel["BASIC"] = "basic";
    ResponseLogLevel["DETAILED"] = "detailed";
    ResponseLogLevel["DEBUG"] = "debug";
})(ResponseLogLevel || (exports.ResponseLogLevel = ResponseLogLevel = {}));
class ResponseLoggingUtils {
    static shouldExcludePath(path, config) {
        return config.excludePaths.some(excludePath => path.includes(excludePath));
    }
    static shouldDetailedLog(path, config) {
        return config.detailedLogPaths.some(detailedPath => path.startsWith(detailedPath));
    }
    static isSlowRequest(responseTime, config) {
        return responseTime > config.slowRequestThreshold;
    }
    static truncateData(data, maxSize, type) {
        if (!data)
            return data;
        const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
        if (dataStr.length > maxSize) {
            const truncated = dataStr.substring(0, maxSize);
            return {
                ...data,
                _truncated: true,
                _originalSize: dataStr.length,
                _truncatedAt: maxSize,
                _type: type
            };
        }
        return data;
    }
    static generateLogTags(req, res, responseTime, config) {
        const tags = [];
        tags.push(`method:${req.method}`);
        if (res.statusCode >= 200 && res.statusCode < 300) {
            tags.push('status:success');
        }
        else if (res.statusCode >= 400 && res.statusCode < 500) {
            tags.push('status:client_error');
        }
        else if (res.statusCode >= 500) {
            tags.push('status:server_error');
        }
        if (this.isSlowRequest(responseTime, config)) {
            tags.push('performance:slow');
        }
        else if (responseTime < 100) {
            tags.push('performance:fast');
        }
        else {
            tags.push('performance:normal');
        }
        if (this.shouldDetailedLog(req.url, config)) {
            tags.push('log_type:detailed');
        }
        else {
            tags.push('log_type:basic');
        }
        return tags;
    }
    static formatLogMessage(req, res, responseTime, traceId) {
        const prefix = traceId ? `[${traceId}]` : '';
        const method = req.method;
        const url = req.url;
        const status = res.statusCode;
        const time = `${responseTime}ms`;
        return `${prefix} ${method} ${url} ${status} ${time}`;
    }
}
exports.ResponseLoggingUtils = ResponseLoggingUtils;
class DataSanitizer {
    static sanitize(data, sensitiveFields) {
        if (!data || typeof data !== 'object')
            return data;
        const sanitized = Array.isArray(data) ? [...data] : { ...data };
        for (const key in sanitized) {
            if (sanitized.hasOwnProperty(key)) {
                if (this.isSensitiveField(key, sensitiveFields)) {
                    sanitized[key] = this.maskValue(sanitized[key]);
                }
                else if (typeof sanitized[key] === 'object') {
                    sanitized[key] = this.sanitize(sanitized[key], sensitiveFields);
                }
            }
        }
        return sanitized;
    }
    static isSensitiveField(fieldName, sensitiveFields) {
        const lowerFieldName = fieldName.toLowerCase();
        return sensitiveFields.some(sensitive => lowerFieldName.includes(sensitive.toLowerCase()));
    }
    static maskValue(value) {
        if (typeof value === 'string') {
            if (value.length <= 4) {
                return '***';
            }
            else if (value.length <= 8) {
                return value.substring(0, 2) + '***' + value.substring(value.length - 2);
            }
            else {
                return value.substring(0, 4) + '***' + value.substring(value.length - 4);
            }
        }
        return '***MASKED***';
    }
}
exports.DataSanitizer = DataSanitizer;
//# sourceMappingURL=response-logging.config.js.map
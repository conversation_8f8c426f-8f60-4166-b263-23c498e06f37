"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResourceLockedException = exports.DuplicateOperationException = exports.InsufficientBalanceException = exports.TokenExpiredException = exports.PermissionDeniedException = exports.UserNotFoundException = exports.BusinessException = exports.UnifiedBusinessException = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("../../web/http_response_result/http-response.interface");
class UnifiedBusinessException extends common_1.HttpException {
    errorType;
    errorDetails;
    businessCode;
    constructor(businessCode, message, data, details) {
        const httpStatus = (0, http_response_interface_1.getHttpStatusCode)(businessCode);
        const response = {
            code: businessCode,
            msg: message || UnifiedBusinessException.getDefaultMessage(businessCode),
            data: data || null
        };
        super(response, httpStatus);
        this.businessCode = businessCode;
        this.errorType = (0, http_response_interface_1.getErrorType)(businessCode);
        this.errorDetails = details;
        this.name = 'UnifiedBusinessException';
    }
    static getDefaultMessage(code) {
        const messages = {
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_DISABLED]: '用户已被禁用',
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_LOCKED]: '用户已被锁定',
            [http_response_interface_1.BUSINESS_ERROR_CODES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
            [http_response_interface_1.BUSINESS_ERROR_CODES.TOKEN_INVALID]: '登录凭证无效',
            [http_response_interface_1.BUSINESS_ERROR_CODES.LOGIN_FAILED]: '登录失败',
            [http_response_interface_1.BUSINESS_ERROR_CODES.PASSWORD_INCORRECT]: '密码错误',
            [http_response_interface_1.BUSINESS_ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定',
            [http_response_interface_1.BUSINESS_ERROR_CODES.PERMISSION_DENIED]: '权限不足',
            [http_response_interface_1.BUSINESS_ERROR_CODES.ROLE_NOT_FOUND]: '角色不存在',
            [http_response_interface_1.BUSINESS_ERROR_CODES.INSUFFICIENT_PRIVILEGES]: '权限不足',
            [http_response_interface_1.BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
            [http_response_interface_1.BUSINESS_ERROR_CODES.OPERATION_NOT_ALLOWED]: '操作不被允许',
            [http_response_interface_1.BUSINESS_ERROR_CODES.RESOURCE_LOCKED]: '资源已被锁定',
            [http_response_interface_1.BUSINESS_ERROR_CODES.DUPLICATE_OPERATION]: '请勿重复操作',
            [http_response_interface_1.BUSINESS_ERROR_CODES.RESOURCE_EXPIRED]: '资源已过期',
            [http_response_interface_1.BUSINESS_ERROR_CODES.SYSTEM_MAINTENANCE]: '系统维护中',
            [http_response_interface_1.BUSINESS_ERROR_CODES.RATE_LIMIT_EXCEEDED]: '操作过于频繁，请稍后重试',
            [http_response_interface_1.BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR]: '外部服务异常',
            [http_response_interface_1.BUSINESS_ERROR_CODES.DATABASE_ERROR]: '数据库异常',
            [http_response_interface_1.BUSINESS_ERROR_CODES.CACHE_ERROR]: '缓存异常',
        };
        return messages[code] || '业务处理异常';
    }
    static userNotFound(userId, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.USER_NOT_FOUND, '用户不存在', { userId }, details);
    }
    static userAlreadyExists(identifier, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.USER_ALREADY_EXISTS, '用户已存在', { identifier }, details);
    }
    static tokenExpired(details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.TOKEN_EXPIRED, '登录已过期，请重新登录', null, details);
    }
    static permissionDenied(resource, action, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.PERMISSION_DENIED, '权限不足', { resource, action }, details);
    }
    static insufficientBalance(currentBalance, requiredAmount, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE, '余额不足', { currentBalance, requiredAmount }, details);
    }
    static duplicateOperation(operation, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.DUPLICATE_OPERATION, '请勿重复操作', { operation }, details);
    }
    static resourceLocked(resourceType, resourceId, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.RESOURCE_LOCKED, '资源已被锁定', { resourceType, resourceId }, details);
    }
    static systemMaintenance(estimatedTime, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.SYSTEM_MAINTENANCE, '系统维护中，请稍后重试', { estimatedTime }, details);
    }
    static rateLimitExceeded(retryAfter, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.RATE_LIMIT_EXCEEDED, '操作过于频繁，请稍后重试', { retryAfter }, details);
    }
    static externalServiceError(serviceName, details) {
        return new UnifiedBusinessException(http_response_interface_1.BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR, '外部服务异常', { serviceName }, details);
    }
}
exports.UnifiedBusinessException = UnifiedBusinessException;
class BusinessException extends UnifiedBusinessException {
}
exports.BusinessException = BusinessException;
class UserNotFoundException extends UnifiedBusinessException {
    constructor(userId, details) {
        super(http_response_interface_1.BUSINESS_ERROR_CODES.USER_NOT_FOUND, '用户不存在', { userId }, details);
    }
}
exports.UserNotFoundException = UserNotFoundException;
class PermissionDeniedException extends UnifiedBusinessException {
    constructor(resource, action, details) {
        super(http_response_interface_1.BUSINESS_ERROR_CODES.PERMISSION_DENIED, '权限不足', { resource, action }, details);
    }
}
exports.PermissionDeniedException = PermissionDeniedException;
class TokenExpiredException extends UnifiedBusinessException {
    constructor(details) {
        super(http_response_interface_1.BUSINESS_ERROR_CODES.TOKEN_EXPIRED, '登录已过期，请重新登录', null, details);
    }
}
exports.TokenExpiredException = TokenExpiredException;
class InsufficientBalanceException extends UnifiedBusinessException {
    constructor(currentBalance, requiredAmount, details) {
        super(http_response_interface_1.BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE, '余额不足', { currentBalance, requiredAmount }, details);
    }
}
exports.InsufficientBalanceException = InsufficientBalanceException;
class DuplicateOperationException extends UnifiedBusinessException {
    constructor(operation, details) {
        super(http_response_interface_1.BUSINESS_ERROR_CODES.DUPLICATE_OPERATION, '请勿重复操作', { operation }, details);
    }
}
exports.DuplicateOperationException = DuplicateOperationException;
class ResourceLockedException extends UnifiedBusinessException {
    constructor(resourceType, resourceId, details) {
        super(http_response_interface_1.BUSINESS_ERROR_CODES.RESOURCE_LOCKED, '资源已被锁定', { resourceType, resourceId }, details);
    }
}
exports.ResourceLockedException = ResourceLockedException;
//# sourceMappingURL=unified-business.exception.js.map
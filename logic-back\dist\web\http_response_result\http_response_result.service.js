"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpResponseResultService = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("./http-response.interface");
const logger_service_1 = require("../../common/logger/logger.service");
let HttpResponseResultService = class HttpResponseResultService {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    success(data, msg = '操作成功', code = http_response_interface_1.SUCCESS_CODE) {
        return {
            code,
            msg,
            data: data,
        };
    }
    error(msg = '系统错误', data, code = http_response_interface_1.ERROR_CODE) {
        return {
            code,
            msg,
            data: data,
        };
    }
    custom(code, msg, data) {
        return {
            code,
            msg,
            data: data,
        };
    }
    enhancedSuccess(data, msg = '操作成功', code = http_response_interface_1.SUCCESS_CODE, options) {
        const response = {
            code,
            msg,
            data: data,
            success: true,
        };
        if (options?.includeTimestamp !== false) {
            response.timestamp = new Date().toISOString();
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.fromCache !== undefined) {
            response.fromCache = options.fromCache;
        }
        if (options?.meta) {
            response.meta = options.meta;
        }
        if (options?.logContext && this.loggerService) {
            this.loggerService.logApiResponse(options.logContext.module, options.logContext.operation, {
                requestData: options.logContext.requestData,
                responseData: data,
                success: true,
                executionTime: options.executionTime,
                userId: options.logContext.userId,
                traceId: options.meta?.requestId
            });
        }
        return response;
    }
    enhancedError(msg = '系统错误', data, code = http_response_interface_1.ERROR_CODE, options) {
        const response = {
            code,
            msg,
            data: data,
            success: false,
        };
        if (options?.includeTimestamp !== false) {
            response.timestamp = new Date().toISOString();
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.meta) {
            response.meta = options.meta;
        }
        if (options?.logContext && this.loggerService) {
            this.loggerService.logApiResponse(options.logContext.module, options.logContext.operation, {
                requestData: options.logContext.requestData,
                responseData: data,
                success: false,
                executionTime: options.executionTime,
                userId: options.logContext.userId,
                traceId: options.meta?.requestId,
                error: options.logContext.error
            });
        }
        return response;
    }
    enhancedCustom(code, msg, data, success = true, options) {
        const response = {
            code,
            msg,
            data: data,
            success,
        };
        if (options?.includeTimestamp !== false) {
            response.timestamp = new Date().toISOString();
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.fromCache !== undefined) {
            response.fromCache = options.fromCache;
        }
        if (options?.meta) {
            response.meta = options.meta;
        }
        return response;
    }
};
exports.HttpResponseResultService = HttpResponseResultService;
exports.HttpResponseResultService = HttpResponseResultService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Optional)()),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], HttpResponseResultService);
//# sourceMappingURL=http_response_result.service.js.map
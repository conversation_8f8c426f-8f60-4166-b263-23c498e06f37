"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpResponseResultService = void 0;
const common_1 = require("@nestjs/common");
const http_response_interface_1 = require("./http-response.interface");
const logger_service_1 = require("../../common/logger/logger.service");
let HttpResponseResultService = class HttpResponseResultService {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    success(data, msg = '操作成功', code = http_response_interface_1.SUCCESS_CODE) {
        return {
            code,
            msg,
            data: data,
        };
    }
    error(msg = '系统错误', data, code = http_response_interface_1.ERROR_CODE) {
        return {
            code,
            msg,
            data: data,
        };
    }
    custom(code, msg, data) {
        return {
            code,
            msg,
            data: data,
        };
    }
    enhancedSuccess(data, msg = '操作成功', code = http_response_interface_1.SUCCESS_CODE, options) {
        const response = {
            code,
            msg,
            data: data,
            success: true,
        };
        if (options?.includeTimestamp !== false) {
            response.timestamp = new Date().toISOString();
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.fromCache !== undefined) {
            response.fromCache = options.fromCache;
        }
        if (options?.meta) {
            response.meta = options.meta;
        }
        if (options?.logContext && this.loggerService) {
            this.loggerService.logApiResponse(options.logContext.module, options.logContext.operation, {
                requestData: options.logContext.requestData,
                responseData: data,
                success: true,
                executionTime: options.executionTime,
                userId: options.logContext.userId,
                traceId: options.meta?.requestId
            });
        }
        return response;
    }
    enhancedError(msg = '系统错误', data, code = http_response_interface_1.ERROR_CODE, options) {
        const response = {
            code,
            msg,
            data: data,
            success: false,
        };
        if (options?.includeTimestamp !== false) {
            response.timestamp = new Date().toISOString();
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.meta) {
            response.meta = options.meta;
        }
        if (options?.logContext && this.loggerService) {
            this.loggerService.logApiResponse(options.logContext.module, options.logContext.operation, {
                requestData: options.logContext.requestData,
                responseData: data,
                success: false,
                executionTime: options.executionTime,
                userId: options.logContext.userId,
                traceId: options.meta?.requestId,
                error: options.logContext.error
            });
        }
        return response;
    }
    enhancedCustom(code, msg, data, success = true, options) {
        const response = {
            code,
            msg,
            data: data,
            success,
        };
        if (options?.includeTimestamp !== false) {
            response.timestamp = new Date().toISOString();
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.fromCache !== undefined) {
            response.fromCache = options.fromCache;
        }
        if (options?.meta) {
            response.meta = options.meta;
        }
        return response;
    }
    unifiedSuccess(data, msg = '操作成功', code = http_response_interface_1.SUCCESS_CODE, options) {
        const response = {
            code,
            msg,
            data: data,
            success: true,
            timestamp: new Date().toISOString(),
        };
        if (options?.requestId || options?.traceId || options?.path) {
            response.trace = {
                requestId: options.requestId,
                traceId: options.traceId,
                path: options.path
            };
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        if (options?.fromCache !== undefined) {
            response.fromCache = options.fromCache;
        }
        return response;
    }
    unifiedError(msg = '系统错误', data, code = http_response_interface_1.ERROR_CODE, options) {
        const response = {
            code,
            msg,
            data: data,
            success: false,
            timestamp: new Date().toISOString(),
        };
        if (options?.requestId || options?.traceId || options?.path) {
            response.trace = {
                requestId: options.requestId,
                traceId: options.traceId,
                path: options.path
            };
        }
        if (options?.errorType || options?.errorDetails || options?.stack) {
            response.error = {
                type: options.errorType || (0, http_response_interface_1.getErrorType)(code),
                details: options.errorDetails,
                stack: process.env.NODE_ENV === 'development' ? options.stack : undefined
            };
        }
        if (options?.executionTime !== undefined) {
            response.executionTime = `${options.executionTime}ms`;
        }
        return response;
    }
    businessError(businessCode, customMessage, data, options) {
        const defaultMessage = this.getBusinessErrorMessage(businessCode);
        return this.unifiedError(customMessage || defaultMessage, data, businessCode, {
            ...options,
            errorType: (0, http_response_interface_1.getErrorType)(businessCode),
            errorDetails: options?.errorDetails
        });
    }
    getBusinessErrorMessage(code) {
        const errorMessages = {
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_NOT_FOUND]: '用户不存在',
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_ALREADY_EXISTS]: '用户已存在',
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_DISABLED]: '用户已被禁用',
            [http_response_interface_1.BUSINESS_ERROR_CODES.USER_LOCKED]: '用户已被锁定',
            [http_response_interface_1.BUSINESS_ERROR_CODES.TOKEN_EXPIRED]: '登录已过期，请重新登录',
            [http_response_interface_1.BUSINESS_ERROR_CODES.TOKEN_INVALID]: '登录凭证无效',
            [http_response_interface_1.BUSINESS_ERROR_CODES.LOGIN_FAILED]: '登录失败',
            [http_response_interface_1.BUSINESS_ERROR_CODES.PASSWORD_INCORRECT]: '密码错误',
            [http_response_interface_1.BUSINESS_ERROR_CODES.ACCOUNT_LOCKED]: '账户已被锁定',
            [http_response_interface_1.BUSINESS_ERROR_CODES.PERMISSION_DENIED]: '权限不足',
            [http_response_interface_1.BUSINESS_ERROR_CODES.ROLE_NOT_FOUND]: '角色不存在',
            [http_response_interface_1.BUSINESS_ERROR_CODES.INSUFFICIENT_PRIVILEGES]: '权限不足',
            [http_response_interface_1.BUSINESS_ERROR_CODES.INSUFFICIENT_BALANCE]: '余额不足',
            [http_response_interface_1.BUSINESS_ERROR_CODES.OPERATION_NOT_ALLOWED]: '操作不被允许',
            [http_response_interface_1.BUSINESS_ERROR_CODES.RESOURCE_LOCKED]: '资源已被锁定',
            [http_response_interface_1.BUSINESS_ERROR_CODES.DUPLICATE_OPERATION]: '请勿重复操作',
            [http_response_interface_1.BUSINESS_ERROR_CODES.RESOURCE_EXPIRED]: '资源已过期',
            [http_response_interface_1.BUSINESS_ERROR_CODES.SYSTEM_MAINTENANCE]: '系统维护中',
            [http_response_interface_1.BUSINESS_ERROR_CODES.RATE_LIMIT_EXCEEDED]: '操作过于频繁，请稍后重试',
            [http_response_interface_1.BUSINESS_ERROR_CODES.EXTERNAL_SERVICE_ERROR]: '外部服务异常',
            [http_response_interface_1.BUSINESS_ERROR_CODES.DATABASE_ERROR]: '数据库异常',
            [http_response_interface_1.BUSINESS_ERROR_CODES.CACHE_ERROR]: '缓存异常',
        };
        return errorMessages[code] || '系统错误';
    }
};
exports.HttpResponseResultService = HttpResponseResultService;
exports.HttpResponseResultService = HttpResponseResultService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Optional)()),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], HttpResponseResultService);
//# sourceMappingURL=http_response_result.service.js.map
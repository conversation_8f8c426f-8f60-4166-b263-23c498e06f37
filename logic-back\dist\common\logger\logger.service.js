"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoggerService = void 0;
const common_1 = require("@nestjs/common");
const winston_1 = require("winston");
const nest_winston_1 = require("nest-winston");
const common_2 = require("@nestjs/common");
let LoggerService = class LoggerService {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    log(message, context) {
        this.logger.info(message, { context });
    }
    error(message, trace, context) {
        this.logger.error(message, { context, trace });
    }
    warn(message, context) {
        this.logger.warn(message, { context });
    }
    debug(message, context) {
        this.logger.debug(message, { context });
    }
    verbose(message, context) {
        this.logger.verbose(message, { context });
    }
    logHttpRequest(req, res, responseTime) {
        const logData = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            timestamp: new Date().toISOString()
        };
        this.logger.info('HTTP Request', {
            context: 'HTTP',
            ...logData
        });
    }
    logHttpRequestResponse(req, res, responseTime, details) {
        const logData = {
            method: req.method,
            url: req.url,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            timestamp: new Date().toISOString(),
            traceId: details.traceId || this.generateTraceId(),
            requestQuery: details.requestQuery,
            requestBody: this.sanitizeRequestData(details.requestBody, req.url),
            responseBody: this.sanitizeResponseData(details.responseBody, req.url),
            isSuccess: res.statusCode >= 200 && res.statusCode < 300,
            isError: res.statusCode >= 400,
            isSlowRequest: responseTime > 1000,
        };
        if (logData.isError) {
            this.logger.error(`[${logData.traceId}] HTTP Request Failed`, {
                context: 'HTTP_DETAILED',
                ...logData
            });
        }
        else if (logData.isSlowRequest) {
            this.logger.warn(`[${logData.traceId}] HTTP Slow Request`, {
                context: 'HTTP_DETAILED',
                ...logData
            });
        }
        else {
            this.logger.info(`[${logData.traceId}] HTTP Request Success`, {
                context: 'HTTP_DETAILED',
                ...logData
            });
        }
    }
    logDatabase(operation, table, data, error) {
        const logData = {
            operation,
            table,
            data: data ? JSON.stringify(data) : undefined,
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Database ${operation} failed on ${table}`, {
                context: 'Database',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Database ${operation} on ${table}`, {
                context: 'Database',
                ...logData
            });
        }
    }
    logPayment(action, data, error) {
        const logData = {
            action,
            data: JSON.stringify(data),
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Payment ${action} failed`, {
                context: 'Payment',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Payment ${action}`, {
                context: 'Payment',
                ...logData
            });
        }
    }
    logAuth(action, userId, details, error) {
        const logData = {
            action,
            userId,
            details: details ? JSON.stringify(details) : undefined,
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Auth ${action} failed`, {
                context: 'Auth',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Auth ${action}`, {
                context: 'Auth',
                ...logData
            });
        }
    }
    logBusiness(module, action, data, error) {
        const logData = {
            module,
            action,
            data: data ? JSON.stringify(data) : undefined,
            error: error ? error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (error) {
            this.logger.error(`Business ${module}.${action} failed`, {
                context: 'Business',
                ...logData,
                trace: error.stack
            });
        }
        else {
            this.logger.info(`Business ${module}.${action}`, {
                context: 'Business',
                ...logData
            });
        }
    }
    logStartup(message, details) {
        this.logger.info(message, {
            context: 'Startup',
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    logApiResponse(module, operation, details) {
        const logData = {
            module,
            operation,
            success: details.success,
            executionTime: details.executionTime ? `${details.executionTime}ms` : undefined,
            userId: details.userId,
            traceId: details.traceId || this.generateTraceId(),
            requestData: this.sanitizeLogData(details.requestData),
            responseData: this.sanitizeLogData(details.responseData),
            error: details.error ? details.error.message : undefined,
            timestamp: new Date().toISOString()
        };
        if (details.success) {
            this.logger.info(`[${logData.traceId}] API ${module}.${operation} Success`, {
                context: 'API_RESPONSE',
                ...logData
            });
        }
        else {
            this.logger.error(`[${logData.traceId}] API ${module}.${operation} Failed`, {
                context: 'API_RESPONSE',
                ...logData,
                trace: details.error?.stack
            });
        }
    }
    generateTraceId() {
        return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }
    sanitizeRequestData(data, url) {
        if (!data || typeof data !== 'object')
            return data;
        const sanitized = { ...data };
        const sensitiveFields = ['password', 'oldPassword', 'newPassword', 'token', 'accessToken', 'refreshToken'];
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                sanitized[field] = '***MASKED***';
            }
        });
        if (url.includes('/payment')) {
            const paymentSensitiveFields = ['cardNumber', 'cvv', 'bankAccount', 'qrCode'];
            paymentSensitiveFields.forEach(field => {
                if (sanitized[field]) {
                    sanitized[field] = '***MASKED***';
                }
            });
        }
        return sanitized;
    }
    sanitizeResponseData(data, url) {
        if (!data)
            return data;
        try {
            const parsed = typeof data === 'string' ? JSON.parse(data) : data;
            if (url.includes('/login') || url.includes('/auth')) {
                if (parsed.data && parsed.data.token) {
                    parsed.data.token = '***MASKED***';
                }
                if (parsed.data && parsed.data.refreshToken) {
                    parsed.data.refreshToken = '***MASKED***';
                }
            }
            if (url.includes('/payment')) {
                if (parsed.data && parsed.data.qrCode) {
                    parsed.data.qrCode = '***MASKED***';
                }
                if (parsed.data && parsed.data.orderNo) {
                    const orderNo = parsed.data.orderNo;
                    if (orderNo.length > 8) {
                        parsed.data.orderNo = orderNo.substring(0, 4) + '***' + orderNo.substring(orderNo.length - 4);
                    }
                }
            }
            return parsed;
        }
        catch (error) {
            return data;
        }
    }
    sanitizeLogData(data) {
        if (!data)
            return 'undefined';
        try {
            const sanitized = typeof data === 'object' ? { ...data } : data;
            if (typeof sanitized === 'object') {
                this.recursiveSanitize(sanitized);
            }
            return JSON.stringify(sanitized);
        }
        catch (error) {
            return '[LOG_SANITIZE_ERROR]';
        }
    }
    recursiveSanitize(obj) {
        if (!obj || typeof obj !== 'object')
            return;
        const sensitiveKeys = [
            'password', 'oldPassword', 'newPassword',
            'token', 'accessToken', 'refreshToken',
            'cardNumber', 'cvv', 'bankAccount', 'qrCode',
            'secret', 'key', 'privateKey'
        ];
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive.toLowerCase()))) {
                    obj[key] = '***MASKED***';
                }
                else if (typeof obj[key] === 'object') {
                    this.recursiveSanitize(obj[key]);
                }
            }
        }
    }
    logPerformance(operation, duration, details) {
        this.logger.info(`Performance: ${operation} took ${duration}ms`, {
            context: 'Performance',
            operation,
            duration,
            ...details,
            timestamp: new Date().toISOString()
        });
    }
    logSecurity(event, details, severity = 'medium') {
        const logData = {
            event,
            severity,
            details: JSON.stringify(details),
            timestamp: new Date().toISOString()
        };
        if (severity === 'high') {
            this.logger.error(`Security Alert: ${event}`, {
                context: 'Security',
                ...logData
            });
        }
        else {
            this.logger.warn(`Security Event: ${event}`, {
                context: 'Security',
                ...logData
            });
        }
    }
};
exports.LoggerService = LoggerService;
exports.LoggerService = LoggerService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_2.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], LoggerService);
//# sourceMappingURL=logger.service.js.map
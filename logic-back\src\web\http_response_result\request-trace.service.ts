import { Injectable, Scope } from '@nestjs/common';
import { Request } from 'express';

/**
 * 请求追踪服务
 * 负责生成和管理请求的唯一标识符
 * 确保同一个请求在整个生命周期中使用相同的requestId和traceId
 */
@Injectable({ scope: Scope.REQUEST })
export class RequestTraceService {
  private _requestId: string | null = null;
  private _traceId: string | null = null;
  private _startTime: number;
  private _request: Request | null = null;

  constructor() {
    this._startTime = Date.now();
  }

  /**
   * 初始化请求追踪信息
   * @param request Express请求对象
   */
  initializeFromRequest(request: Request): void {
    this._request = request;
    
    // 尝试从请求头获取已有的追踪ID
    const existingTraceId = request.headers['x-trace-id'] as string;
    const existingRequestId = request.headers['x-request-id'] as string;
    
    // 如果请求头中没有，则生成新的
    this._traceId = existingTraceId || this.generateTraceId();
    this._requestId = existingRequestId || this.generateRequestId();
    
    // 将追踪ID添加到请求对象中，供后续使用
    (request as any).traceId = this._traceId;
    (request as any).requestId = this._requestId;
  }

  /**
   * 获取请求ID
   * 如果还没有生成，则自动生成一个
   */
  getRequestId(): string {
    if (!this._requestId) {
      this._requestId = this.generateRequestId();
    }
    return this._requestId;
  }

  /**
   * 获取追踪ID
   * 如果还没有生成，则自动生成一个
   */
  getTraceId(): string {
    if (!this._traceId) {
      this._traceId = this.generateTraceId();
    }
    return this._traceId;
  }

  /**
   * 获取请求路径
   */
  getPath(): string {
    return this._request?.url || '';
  }

  /**
   * 获取请求执行时间（毫秒）
   */
  getExecutionTime(): number {
    return Date.now() - this._startTime;
  }

  /**
   * 获取完整的追踪信息
   */
  getTraceInfo(): {
    requestId: string;
    traceId: string;
    path: string;
    executionTime: number;
  } {
    return {
      requestId: this.getRequestId(),
      traceId: this.getTraceId(),
      path: this.getPath(),
      executionTime: this.getExecutionTime()
    };
  }

  /**
   * 生成请求ID
   * 格式: req-{timestamp}-{random}
   */
  private generateRequestId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `req-${timestamp}-${random}`;
  }

  /**
   * 生成追踪ID
   * 格式: trace-{timestamp}-{random}
   */
  private generateTraceId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `trace-${timestamp}-${random}`;
  }

  /**
   * 生成业务特定的请求ID
   * @param prefix 业务前缀（如 'ip-loc', 'payment', 'user'）
   */
  generateBusinessRequestId(prefix: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * 设置自定义的请求ID（用于特殊场景）
   * @param requestId 自定义请求ID
   */
  setCustomRequestId(requestId: string): void {
    this._requestId = requestId;
    if (this._request) {
      (this._request as any).requestId = requestId;
    }
  }

  /**
   * 设置自定义的追踪ID（用于特殊场景）
   * @param traceId 自定义追踪ID
   */
  setCustomTraceId(traceId: string): void {
    this._traceId = traceId;
    if (this._request) {
      (this._request as any).traceId = traceId;
    }
  }

  /**
   * 获取客户端IP地址
   */
  getClientIP(): string {
    if (!this._request) return '';
    
    return (
      this._request.headers['x-forwarded-for'] as string ||
      this._request.headers['x-real-ip'] as string ||
      this._request.connection?.remoteAddress ||
      this._request.socket?.remoteAddress ||
      ''
    ).split(',')[0].trim();
  }

  /**
   * 获取用户代理
   */
  getUserAgent(): string {
    return this._request?.headers['user-agent'] || '';
  }

  /**
   * 获取请求方法
   */
  getMethod(): string {
    return this._request?.method || '';
  }

  /**
   * 获取完整的请求上下文信息
   */
  getRequestContext(): {
    requestId: string;
    traceId: string;
    path: string;
    method: string;
    clientIP: string;
    userAgent: string;
    executionTime: number;
    timestamp: string;
  } {
    return {
      requestId: this.getRequestId(),
      traceId: this.getTraceId(),
      path: this.getPath(),
      method: this.getMethod(),
      clientIP: this.getClientIP(),
      userAgent: this.getUserAgent(),
      executionTime: this.getExecutionTime(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 重置追踪信息（用于测试或特殊场景）
   */
  reset(): void {
    this._requestId = null;
    this._traceId = null;
    this._startTime = Date.now();
    this._request = null;
  }
}

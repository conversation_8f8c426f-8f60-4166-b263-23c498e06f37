"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CODE_TO_HTTP_STATUS = exports.ERROR_MESSAGES = exports.ErrorType = exports.BUSINESS_ERROR_CODES = exports.SERVER_ERROR_CODES = exports.CLIENT_ERROR_CODES = exports.SUCCESS_CODES = void 0;
exports.getHttpStatusCode = getHttpStatusCode;
exports.getErrorType = getErrorType;
exports.SUCCESS_CODES = {
    OK: 200,
    CREATED: 201,
    ACCEPTED: 202,
    PARTIAL_SUCCESS: 206,
};
exports.CLIENT_ERROR_CODES = {
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    METHOD_NOT_ALLOWED: 405,
    CONFLICT: 409,
    VALIDATION_ERROR: 422,
    TOO_MANY_REQUESTS: 429,
};
exports.SERVER_ERROR_CODES = {
    INTERNAL_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504,
};
exports.BUSINESS_ERROR_CODES = {
    USER_NOT_FOUND: 1001,
    USER_ALREADY_EXISTS: 1002,
    USER_DISABLED: 1003,
    USER_LOCKED: 1004,
    TOKEN_EXPIRED: 2001,
    TOKEN_INVALID: 2002,
    LOGIN_FAILED: 2003,
    PASSWORD_INCORRECT: 2004,
    ACCOUNT_LOCKED: 2005,
    PERMISSION_DENIED: 3001,
    ROLE_NOT_FOUND: 3002,
    INSUFFICIENT_PRIVILEGES: 3003,
    INSUFFICIENT_BALANCE: 4001,
    OPERATION_NOT_ALLOWED: 4002,
    RESOURCE_LOCKED: 4003,
    DUPLICATE_OPERATION: 4004,
    RESOURCE_EXPIRED: 4005,
    SYSTEM_MAINTENANCE: 5001,
    RATE_LIMIT_EXCEEDED: 5002,
    EXTERNAL_SERVICE_ERROR: 5003,
    DATABASE_ERROR: 5004,
    CACHE_ERROR: 5005,
};
var ErrorType;
(function (ErrorType) {
    ErrorType["SYSTEM_ERROR"] = "SystemError";
    ErrorType["BUSINESS_ERROR"] = "BusinessError";
    ErrorType["VALIDATION_ERROR"] = "ValidationError";
    ErrorType["AUTHENTICATION_ERROR"] = "AuthenticationError";
    ErrorType["AUTHORIZATION_ERROR"] = "AuthorizationError";
    ErrorType["NETWORK_ERROR"] = "NetworkError";
    ErrorType["DATABASE_ERROR"] = "DatabaseError";
    ErrorType["EXTERNAL_SERVICE_ERROR"] = "ExternalServiceError";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
exports.ERROR_MESSAGES = {
    VALIDATION_ERROR: {
        userMessage: '输入信息有误，请检查后重试',
        developerMessage: 'Request validation failed',
        errorCode: 'VALIDATION_ERROR',
    },
    UNAUTHORIZED: {
        userMessage: '请先登录后再访问',
        developerMessage: 'Authentication required',
        errorCode: 'UNAUTHORIZED',
    },
    TOKEN_EXPIRED: {
        userMessage: '登录已过期，请重新登录',
        developerMessage: 'Access token has expired',
        errorCode: 'TOKEN_EXPIRED',
    },
    FORBIDDEN: {
        userMessage: '您没有权限执行此操作',
        developerMessage: 'Insufficient permissions',
        errorCode: 'FORBIDDEN',
    },
    NOT_FOUND: {
        userMessage: '请求的资源不存在',
        developerMessage: 'Requested resource not found',
        errorCode: 'NOT_FOUND',
    },
    INSUFFICIENT_BALANCE: {
        userMessage: '余额不足，请先充值',
        developerMessage: 'User balance is insufficient for this operation',
        errorCode: 'INSUFFICIENT_BALANCE',
    },
    DUPLICATE_OPERATION: {
        userMessage: '请勿重复操作',
        developerMessage: 'Duplicate operation detected',
        errorCode: 'DUPLICATE_OPERATION',
    },
    INTERNAL_ERROR: {
        userMessage: '服务器内部错误，请稍后重试',
        developerMessage: 'Internal server error occurred',
        errorCode: 'INTERNAL_ERROR',
    },
    SERVICE_UNAVAILABLE: {
        userMessage: '服务暂时不可用，请稍后重试',
        developerMessage: 'Service is temporarily unavailable',
        errorCode: 'SERVICE_UNAVAILABLE',
    },
    RATE_LIMIT_EXCEEDED: {
        userMessage: '操作过于频繁，请稍后重试',
        developerMessage: 'Rate limit exceeded',
        errorCode: 'RATE_LIMIT_EXCEEDED',
    },
};
exports.CODE_TO_HTTP_STATUS = {
    200: 200,
    201: 201,
    202: 202,
    206: 206,
    400: 400,
    401: 401,
    403: 403,
    404: 404,
    405: 405,
    409: 409,
    422: 422,
    429: 429,
    500: 500,
    501: 501,
    502: 502,
    503: 503,
    504: 504,
    1001: 400,
    1002: 409,
    1003: 403,
    1004: 423,
    2001: 401,
    2002: 401,
    2003: 401,
    2004: 401,
    2005: 423,
    3001: 403,
    3002: 403,
    3003: 403,
    4001: 400,
    4002: 400,
    4003: 423,
    4004: 409,
    4005: 410,
    5001: 503,
    5002: 429,
    5003: 502,
    5004: 500,
    5005: 500,
};
function getHttpStatusCode(businessCode) {
    return exports.CODE_TO_HTTP_STATUS[businessCode] || 500;
}
function getErrorType(code) {
    if (code >= 1000 && code < 2000)
        return ErrorType.BUSINESS_ERROR;
    if (code >= 2000 && code < 3000)
        return ErrorType.AUTHENTICATION_ERROR;
    if (code >= 3000 && code < 4000)
        return ErrorType.AUTHORIZATION_ERROR;
    if (code >= 4000 && code < 5000)
        return ErrorType.BUSINESS_ERROR;
    if (code >= 5000 && code < 6000)
        return ErrorType.SYSTEM_ERROR;
    if (code === 400 || code === 422)
        return ErrorType.VALIDATION_ERROR;
    if (code === 401)
        return ErrorType.AUTHENTICATION_ERROR;
    if (code === 403)
        return ErrorType.AUTHORIZATION_ERROR;
    if (code >= 500)
        return ErrorType.SYSTEM_ERROR;
    return ErrorType.SYSTEM_ERROR;
}
//# sourceMappingURL=unified-response.interface.js.map
import { NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { RequestTraceService } from './request-trace.service';
export declare class RequestTraceMiddleware implements NestMiddleware {
    private readonly requestTraceService;
    constructor(requestTraceService: RequestTraceService);
    use(req: Request, res: Response, next: NextFunction): void;
}

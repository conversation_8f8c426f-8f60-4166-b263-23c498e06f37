import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Inject } from '@nestjs/common';

@Injectable()
export class LoggerService implements NestLoggerService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger
  ) {}

  /**
   * 记录普通日志
   */
  log(message: any, context?: string) {
    this.logger.info(message, { context });
  }

  /**
   * 记录错误日志
   */
  error(message: any, trace?: string, context?: string) {
    this.logger.error(message, { context, trace });
  }

  /**
   * 记录警告日志
   */
  warn(message: any, context?: string) {
    this.logger.warn(message, { context });
  }

  /**
   * 记录调试日志
   */
  debug(message: any, context?: string) {
    this.logger.debug(message, { context });
  }

  /**
   * 记录详细日志
   */
  verbose(message: any, context?: string) {
    this.logger.verbose(message, { context });
  }

  /**
   * 记录HTTP请求日志（保持向后兼容）
   */
  logHttpRequest(req: any, res: any, responseTime: number) {
    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date().toISOString()
    };

    this.logger.info('HTTP Request', {
      context: 'HTTP',
      ...logData
    });
  }

  /**
   * 记录完整的HTTP请求响应日志
   */
  logHttpRequestResponse(
    req: any,
    res: any,
    responseTime: number,
    details: {
      requestBody?: any;
      requestQuery?: any;
      responseBody?: any;
      traceId?: string;
    }
  ) {
    const logData = {
      // 基础信息
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      timestamp: new Date().toISOString(),

      // 追踪信息
      traceId: details.traceId || this.generateTraceId(),

      // 详细信息
      requestQuery: details.requestQuery,
      requestBody: this.sanitizeRequestData(details.requestBody, req.url),
      responseBody: this.sanitizeResponseData(details.responseBody, req.url),

      // 分类标识
      isSuccess: res.statusCode >= 200 && res.statusCode < 300,
      isError: res.statusCode >= 400,
      isSlowRequest: responseTime > 1000, // 超过1秒的慢请求
    };

    // 根据状态码和响应时间选择日志级别
    if (logData.isError) {
      this.logger.error(`[${logData.traceId}] HTTP Request Failed`, {
        context: 'HTTP_DETAILED',
        ...logData
      });
    } else if (logData.isSlowRequest) {
      this.logger.warn(`[${logData.traceId}] HTTP Slow Request`, {
        context: 'HTTP_DETAILED',
        ...logData
      });
    } else {
      this.logger.info(`[${logData.traceId}] HTTP Request Success`, {
        context: 'HTTP_DETAILED',
        ...logData
      });
    }
  }

  /**
   * 记录数据库操作日志
   */
  logDatabase(operation: string, table: string, data?: any, error?: any) {
    const logData = {
      operation,
      table,
      data: data ? JSON.stringify(data) : undefined,
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Database ${operation} failed on ${table}`, {
        context: 'Database',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Database ${operation} on ${table}`, {
        context: 'Database',
        ...logData
      });
    }
  }

  /**
   * 记录支付相关日志
   */
  logPayment(action: string, data: any, error?: any) {
    const logData = {
      action,
      data: JSON.stringify(data),
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Payment ${action} failed`, {
        context: 'Payment',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Payment ${action}`, {
        context: 'Payment',
        ...logData
      });
    }
  }

  /**
   * 记录认证相关日志
   */
  logAuth(action: string, userId?: string, details?: any, error?: any) {
    const logData = {
      action,
      userId,
      details: details ? JSON.stringify(details) : undefined,
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Auth ${action} failed`, {
        context: 'Auth',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Auth ${action}`, {
        context: 'Auth',
        ...logData
      });
    }
  }

  /**
   * 记录业务逻辑日志
   */
  logBusiness(module: string, action: string, data?: any, error?: any) {
    const logData = {
      module,
      action,
      data: data ? JSON.stringify(data) : undefined,
      error: error ? error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (error) {
      this.logger.error(`Business ${module}.${action} failed`, {
        context: 'Business',
        ...logData,
        trace: error.stack
      });
    } else {
      this.logger.info(`Business ${module}.${action}`, {
        context: 'Business',
        ...logData
      });
    }
  }

  /**
   * 记录系统启动日志
   */
  logStartup(message: string, details?: any) {
    this.logger.info(message, {
      context: 'Startup',
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录API响应日志
   */
  logApiResponse(
    module: string,
    operation: string,
    details: {
      requestData?: any;
      responseData?: any;
      success: boolean;
      executionTime?: number;
      userId?: string;
      traceId?: string;
      error?: Error;
    }
  ) {
    const logData = {
      module,
      operation,
      success: details.success,
      executionTime: details.executionTime ? `${details.executionTime}ms` : undefined,
      userId: details.userId,
      traceId: details.traceId || this.generateTraceId(),
      requestData: this.sanitizeLogData(details.requestData),
      responseData: this.sanitizeLogData(details.responseData),
      error: details.error ? details.error.message : undefined,
      timestamp: new Date().toISOString()
    };

    if (details.success) {
      this.logger.info(`[${logData.traceId}] API ${module}.${operation} Success`, {
        context: 'API_RESPONSE',
        ...logData
      });
    } else {
      this.logger.error(`[${logData.traceId}] API ${module}.${operation} Failed`, {
        context: 'API_RESPONSE',
        ...logData,
        trace: details.error?.stack
      });
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 生成追踪ID
   */
  private generateTraceId(): string {
    return `trace-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 敏感请求数据脱敏
   */
  private sanitizeRequestData(data: any, url: string): any {
    if (!data || typeof data !== 'object') return data;

    const sanitized = { ...data };

    // 密码相关字段脱敏
    const sensitiveFields = ['password', 'oldPassword', 'newPassword', 'token', 'accessToken', 'refreshToken'];
    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***MASKED***';
      }
    });

    // 支付相关字段脱敏
    if (url.includes('/payment')) {
      const paymentSensitiveFields = ['cardNumber', 'cvv', 'bankAccount', 'qrCode'];
      paymentSensitiveFields.forEach(field => {
        if (sanitized[field]) {
          sanitized[field] = '***MASKED***';
        }
      });
    }

    return sanitized;
  }

  /**
   * 敏感响应数据脱敏
   */
  private sanitizeResponseData(data: any, url: string): any {
    if (!data) return data;

    try {
      const parsed = typeof data === 'string' ? JSON.parse(data) : data;

      // 对敏感接口进行数据脱敏
      if (url.includes('/login') || url.includes('/auth')) {
        if (parsed.data && parsed.data.token) {
          parsed.data.token = '***MASKED***';
        }
        if (parsed.data && parsed.data.refreshToken) {
          parsed.data.refreshToken = '***MASKED***';
        }
      }

      if (url.includes('/payment')) {
        // 支付相关敏感信息脱敏
        if (parsed.data && parsed.data.qrCode) {
          parsed.data.qrCode = '***MASKED***';
        }
        if (parsed.data && parsed.data.orderNo) {
          // 订单号部分脱敏，保留前后几位
          const orderNo = parsed.data.orderNo;
          if (orderNo.length > 8) {
            parsed.data.orderNo = orderNo.substring(0, 4) + '***' + orderNo.substring(orderNo.length - 4);
          }
        }
      }

      return parsed;
    } catch (error) {
      return data;
    }
  }

  /**
   * 通用日志数据脱敏
   */
  private sanitizeLogData(data: any): string {
    if (!data) return 'undefined';

    try {
      const sanitized = typeof data === 'object' ? { ...data } : data;

      // 递归脱敏对象中的敏感字段
      if (typeof sanitized === 'object') {
        this.recursiveSanitize(sanitized);
      }

      return JSON.stringify(sanitized);
    } catch (error) {
      return '[LOG_SANITIZE_ERROR]';
    }
  }

  /**
   * 递归脱敏对象
   */
  private recursiveSanitize(obj: any): void {
    if (!obj || typeof obj !== 'object') return;

    const sensitiveKeys = [
      'password', 'oldPassword', 'newPassword',
      'token', 'accessToken', 'refreshToken',
      'cardNumber', 'cvv', 'bankAccount', 'qrCode',
      'secret', 'key', 'privateKey'
    ];

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive.toLowerCase()))) {
          obj[key] = '***MASKED***';
        } else if (typeof obj[key] === 'object') {
          this.recursiveSanitize(obj[key]);
        }
      }
    }
  }

  /**
   * 记录性能日志
   */
  logPerformance(operation: string, duration: number, details?: any) {
    this.logger.info(`Performance: ${operation} took ${duration}ms`, {
      context: 'Performance',
      operation,
      duration,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 记录安全相关日志
   */
  logSecurity(event: string, details: any, severity: 'low' | 'medium' | 'high' = 'medium') {
    const logData = {
      event,
      severity,
      details: JSON.stringify(details),
      timestamp: new Date().toISOString()
    };

    if (severity === 'high') {
      this.logger.error(`Security Alert: ${event}`, {
        context: 'Security',
        ...logData
      });
    } else {
      this.logger.warn(`Security Event: ${event}`, {
        context: 'Security',
        ...logData
      });
    }
  }
}

# 统一请求追踪集成指南

## 🎯 问题解决

### **原始问题**
每个服务都在各自生成 `requestId`，导致：
- ❌ 同一个请求的成功和失败响应有不同的 `requestId`
- ❌ 代码重复，维护困难
- ❌ 无法进行完整的请求追踪

### **解决方案**
创建统一的 `RequestTraceService`，确保整个请求生命周期中使用相同的 `requestId` 和 `traceId`。

## 🏗️ 架构设计

### **核心组件**

#### **1. RequestTraceService（请求级作用域）**
```typescript
@Injectable({ scope: Scope.REQUEST })
export class RequestTraceService {
  private _requestId: string | null = null;
  private _traceId: string | null = null;
  private _startTime: number;
  
  // 自动生成和管理请求追踪信息
  getRequestId(): string;
  getTraceId(): string;
  getExecutionTime(): number;
  getTraceInfo(): TraceInfo;
}
```

#### **2. RequestTraceMiddleware（中间件）**
```typescript
@Injectable()
export class RequestTraceMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // 初始化请求追踪信息
    this.requestTraceService.initializeFromRequest(req);
    
    // 在响应头中添加追踪信息
    res.setHeader('X-Request-ID', requestId);
    res.setHeader('X-Trace-ID', traceId);
    
    next();
  }
}
```

#### **3. 集成到HttpResponseResultService**
```typescript
export class HttpResponseResultService {
  constructor(
    @Optional() private readonly requestTraceService?: RequestTraceService
  ) {}
  
  unifiedSuccess<T>(data?: T, msg?: string, code?: number, options?: any): UnifiedResponse<T> {
    // 自动获取追踪信息
    if (this.requestTraceService) {
      const traceInfo = this.requestTraceService.getTraceInfo();
      response.trace = {
        requestId: traceInfo.requestId,
        traceId: traceInfo.traceId,
        path: traceInfo.path
      };
    }
    
    return response;
  }
}
```

## 🚀 使用方式

### **1. 自动追踪（推荐）**

#### **成功响应**
```typescript
// 不需要手动传入requestId和traceId
return this.httpResponseService.unifiedSuccess(
  data,
  '操作成功',
  200,
  {
    // RequestTraceService会自动提供requestId和traceId
    fromCache: false
  }
);
```

#### **错误响应**
```typescript
// 不需要手动传入requestId和traceId
return this.httpResponseService.unifiedError(
  '操作失败',
  null,
  500,
  {
    // RequestTraceService会自动提供requestId和traceId
    errorDetails: { reason: 'Database connection failed' }
  }
);
```

#### **业务错误**
```typescript
// 不需要手动传入requestId和traceId
return this.httpResponseService.businessError(
  BUSINESS_ERROR_CODES.USER_NOT_FOUND,
  '用户不存在',
  { userId: 123 },
  {
    // RequestTraceService会自动提供requestId和traceId
    errorDetails: { searchMethod: 'findById' }
  }
);
```

### **2. 手动覆盖（特殊场景）**

```typescript
// 如果需要自定义requestId或traceId
return this.httpResponseService.unifiedSuccess(
  data,
  '操作成功',
  200,
  {
    requestId: 'custom-req-123',  // 覆盖自动生成的requestId
    traceId: 'custom-trace-456',  // 覆盖自动生成的traceId
    path: '/custom/path'          // 覆盖自动获取的path
  }
);
```

### **3. 业务特定的请求ID**

```typescript
@Injectable()
export class PaymentService {
  constructor(
    private readonly requestTraceService: RequestTraceService
  ) {}
  
  async processPayment(paymentData: any) {
    // 生成业务特定的请求ID
    const paymentRequestId = this.requestTraceService.generateBusinessRequestId('payment');
    
    // 使用业务请求ID进行日志记录
    this.logger.log(`[${paymentRequestId}] Processing payment...`);
    
    // 响应中会自动使用统一的requestId
    return this.httpResponseService.unifiedSuccess(result, '支付成功');
  }
}
```

## 📊 响应格式对比

### **修复前（❌ 不一致）**
```typescript
// 成功响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {...},
  "trace": {
    "requestId": "ip-loc-1722571479928-abc123"  // 手动生成
  }
}

// 失败响应（同一个请求）
{
  "code": 500,
  "msg": "操作失败",
  "data": null,
  "trace": {
    "requestId": "ip-loc-1722571480156-def456"  // 不同的requestId！
  }
}
```

### **修复后（✅ 一致）**
```typescript
// 成功响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {...},
  "success": true,
  "timestamp": "2025-08-02T04:30:00.000Z",
  "trace": {
    "requestId": "req-1722571479928-abc123",    // 统一管理
    "traceId": "trace-1722571479928-xyz789",   // 全链路追踪
    "path": "/api/v1/ip-location/current"
  }
}

// 失败响应（同一个请求）
{
  "code": 500,
  "msg": "操作失败",
  "data": null,
  "success": false,
  "timestamp": "2025-08-02T04:30:00.000Z",
  "trace": {
    "requestId": "req-1722571479928-abc123",    // 相同的requestId！
    "traceId": "trace-1722571479928-xyz789",   // 相同的traceId！
    "path": "/api/v1/ip-location/current"
  },
  "error": {
    "type": "SystemError",
    "details": {...}
  }
}
```

## 🔧 配置步骤

### **1. 在主应用模块中配置中间件**
```typescript
// app.module.ts
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 请求追踪中间件（优先级最高）
    consumer
      .apply(RequestTraceMiddleware)
      .forRoutes('*');
      
    // 其他中间件...
  }
}
```

### **2. 在服务中注入RequestTraceService**
```typescript
@Injectable()
export class YourService {
  constructor(
    private readonly httpResponseService: HttpResponseResultService,
    @Optional() private readonly requestTraceService?: RequestTraceService
  ) {}
  
  async yourMethod() {
    // 直接使用，无需手动传入requestId
    return this.httpResponseService.unifiedSuccess(data, '操作成功');
  }
}
```

### **3. 响应头自动添加**
```typescript
// 每个响应都会自动包含以下响应头
X-Request-ID: req-1722571479928-abc123
X-Trace-ID: trace-1722571479928-xyz789
```

## 🎯 最佳实践

### **1. 统一使用自动追踪**
```typescript
// ✅ 推荐：让RequestTraceService自动处理
return this.httpResponseService.unifiedSuccess(data, '操作成功');

// ❌ 不推荐：手动生成requestId
const requestId = this.generateRequestId();
return this.httpResponseService.unifiedSuccess(data, '操作成功', 200, { requestId });
```

### **2. 业务日志使用统一追踪ID**
```typescript
@Injectable()
export class YourService {
  async yourMethod() {
    const { requestId, traceId } = this.requestTraceService.getTraceInfo();
    
    // 在日志中使用统一的追踪ID
    this.logger.log(`[${traceId}] Processing request ${requestId}...`);
    
    try {
      const result = await this.processBusinessLogic();
      this.logger.log(`[${traceId}] Request ${requestId} completed successfully`);
      
      return this.httpResponseService.unifiedSuccess(result, '操作成功');
    } catch (error) {
      this.logger.error(`[${traceId}] Request ${requestId} failed: ${error.message}`);
      
      return this.httpResponseService.unifiedError('操作失败', null, 500);
    }
  }
}
```

### **3. 前端请求追踪**
```typescript
// 前端可以在请求头中传入自定义的追踪ID
const response = await fetch('/api/v1/users', {
  headers: {
    'X-Trace-ID': 'frontend-trace-123',
    'X-Request-ID': 'frontend-req-456'
  }
});

// 后端会使用前端传入的追踪ID，实现全链路追踪
```

## 🔍 调试和监控

### **1. 日志关联**
```bash
# 通过traceId查找所有相关日志
grep "trace-1722571479928-xyz789" logs/application.log

# 通过requestId查找特定请求的所有日志
grep "req-1722571479928-abc123" logs/application.log
```

### **2. 性能监控**
```typescript
// RequestTraceService自动计算执行时间
const { executionTime } = this.requestTraceService.getTraceInfo();
console.log(`Request completed in ${executionTime}ms`);
```

### **3. 错误追踪**
```typescript
// 错误响应中包含完整的追踪信息
{
  "trace": {
    "requestId": "req-1722571479928-abc123",
    "traceId": "trace-1722571479928-xyz789",
    "path": "/api/v1/users/123"
  },
  "error": {
    "type": "BusinessError",
    "details": {...}
  }
}
```

## 🎉 总结

通过统一的请求追踪服务，我们实现了：

1. **✅ 请求ID唯一性**：同一个请求在整个生命周期中使用相同的requestId
2. **✅ 全链路追踪**：通过traceId实现跨服务的请求追踪
3. **✅ 自动化管理**：无需手动生成和传递追踪ID
4. **✅ 向后兼容**：现有代码可以无缝升级
5. **✅ 调试友好**：通过响应头和日志轻松追踪问题
6. **✅ 性能监控**：自动计算请求执行时间

这套方案既简化了开发工作，又提供了强大的追踪和调试能力！🐱

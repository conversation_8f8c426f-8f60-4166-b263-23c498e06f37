/**
 * 统一响应接口定义
 */
export interface UnifiedResponse<T = any> {
  /** 业务状态码 */
  code: number;
  
  /** 响应消息 */
  msg: string;
  
  /** 响应数据 */
  data: T | null;
  
  /** 成功标识 */
  success: boolean;
  
  /** 时间戳 */
  timestamp: string;
  
  /** 追踪信息（可选） */
  trace?: {
    /** 请求ID */
    requestId?: string;
    /** 追踪ID */
    traceId?: string;
    /** 请求路径 */
    path?: string;
  };
  
  /** 错误详情（仅错误响应） */
  error?: {
    /** 错误类型 */
    type: string;
    /** 错误详情 */
    details?: any;
    /** 错误堆栈（仅开发环境） */
    stack?: string;
  };
}

/**
 * 成功状态码
 */
export const SUCCESS_CODES = {
  OK: 200,                    // 操作成功
  CREATED: 201,              // 创建成功
  ACCEPTED: 202,             // 已接受处理
  PARTIAL_SUCCESS: 206,      // 部分成功
} as const;

/**
 * 客户端错误状态码
 */
export const CLIENT_ERROR_CODES = {
  BAD_REQUEST: 400,          // 请求参数错误
  UNAUTHORIZED: 401,         // 未授权
  FORBIDDEN: 403,            // 权限不足
  NOT_FOUND: 404,            // 资源不存在
  METHOD_NOT_ALLOWED: 405,   // 方法不允许
  CONFLICT: 409,             // 资源冲突
  VALIDATION_ERROR: 422,     // 数据验证错误
  TOO_MANY_REQUESTS: 429,    // 请求过于频繁
} as const;

/**
 * 服务器错误状态码
 */
export const SERVER_ERROR_CODES = {
  INTERNAL_ERROR: 500,       // 服务器内部错误
  NOT_IMPLEMENTED: 501,      // 功能未实现
  BAD_GATEWAY: 502,          // 网关错误
  SERVICE_UNAVAILABLE: 503,  // 服务不可用
  GATEWAY_TIMEOUT: 504,      // 网关超时
} as const;

/**
 * 业务错误状态码
 */
export const BUSINESS_ERROR_CODES = {
  // 用户相关 (1000-1999)
  USER_NOT_FOUND: 1001,
  USER_ALREADY_EXISTS: 1002,
  USER_DISABLED: 1003,
  USER_LOCKED: 1004,
  
  // 认证相关 (2000-2999)
  TOKEN_EXPIRED: 2001,
  TOKEN_INVALID: 2002,
  LOGIN_FAILED: 2003,
  PASSWORD_INCORRECT: 2004,
  ACCOUNT_LOCKED: 2005,
  
  // 权限相关 (3000-3999)
  PERMISSION_DENIED: 3001,
  ROLE_NOT_FOUND: 3002,
  INSUFFICIENT_PRIVILEGES: 3003,
  
  // 业务逻辑相关 (4000-4999)
  INSUFFICIENT_BALANCE: 4001,
  OPERATION_NOT_ALLOWED: 4002,
  RESOURCE_LOCKED: 4003,
  DUPLICATE_OPERATION: 4004,
  RESOURCE_EXPIRED: 4005,
  
  // 系统相关 (5000-5999)
  SYSTEM_MAINTENANCE: 5001,
  RATE_LIMIT_EXCEEDED: 5002,
  EXTERNAL_SERVICE_ERROR: 5003,
  DATABASE_ERROR: 5004,
  CACHE_ERROR: 5005,
} as const;

/**
 * 错误类型枚举
 */
export enum ErrorType {
  SYSTEM_ERROR = 'SystemError',
  BUSINESS_ERROR = 'BusinessError',
  VALIDATION_ERROR = 'ValidationError',
  AUTHENTICATION_ERROR = 'AuthenticationError',
  AUTHORIZATION_ERROR = 'AuthorizationError',
  NETWORK_ERROR = 'NetworkError',
  DATABASE_ERROR = 'DatabaseError',
  EXTERNAL_SERVICE_ERROR = 'ExternalServiceError',
}

/**
 * 错误消息接口
 */
export interface ErrorMessage {
  /** 用户友好的错误消息 */
  userMessage: string;
  
  /** 开发者错误消息 */
  developerMessage: string;
  
  /** 错误代码 */
  errorCode: string;
  
  /** 帮助链接（可选） */
  moreInfo?: string;
}

/**
 * 预定义错误消息
 */
export const ERROR_MESSAGES: Record<string, ErrorMessage> = {
  // 验证错误
  VALIDATION_ERROR: {
    userMessage: '输入信息有误，请检查后重试',
    developerMessage: 'Request validation failed',
    errorCode: 'VALIDATION_ERROR',
  },
  
  // 认证错误
  UNAUTHORIZED: {
    userMessage: '请先登录后再访问',
    developerMessage: 'Authentication required',
    errorCode: 'UNAUTHORIZED',
  },
  
  TOKEN_EXPIRED: {
    userMessage: '登录已过期，请重新登录',
    developerMessage: 'Access token has expired',
    errorCode: 'TOKEN_EXPIRED',
  },
  
  // 权限错误
  FORBIDDEN: {
    userMessage: '您没有权限执行此操作',
    developerMessage: 'Insufficient permissions',
    errorCode: 'FORBIDDEN',
  },
  
  // 资源错误
  NOT_FOUND: {
    userMessage: '请求的资源不存在',
    developerMessage: 'Requested resource not found',
    errorCode: 'NOT_FOUND',
  },
  
  // 业务错误
  INSUFFICIENT_BALANCE: {
    userMessage: '余额不足，请先充值',
    developerMessage: 'User balance is insufficient for this operation',
    errorCode: 'INSUFFICIENT_BALANCE',
  },
  
  DUPLICATE_OPERATION: {
    userMessage: '请勿重复操作',
    developerMessage: 'Duplicate operation detected',
    errorCode: 'DUPLICATE_OPERATION',
  },
  
  // 系统错误
  INTERNAL_ERROR: {
    userMessage: '服务器内部错误，请稍后重试',
    developerMessage: 'Internal server error occurred',
    errorCode: 'INTERNAL_ERROR',
  },
  
  SERVICE_UNAVAILABLE: {
    userMessage: '服务暂时不可用，请稍后重试',
    developerMessage: 'Service is temporarily unavailable',
    errorCode: 'SERVICE_UNAVAILABLE',
  },
  
  RATE_LIMIT_EXCEEDED: {
    userMessage: '操作过于频繁，请稍后重试',
    developerMessage: 'Rate limit exceeded',
    errorCode: 'RATE_LIMIT_EXCEEDED',
  },
};

/**
 * 状态码到HTTP状态码的映射
 */
export const CODE_TO_HTTP_STATUS: Record<number, number> = {
  // 成功状态码
  200: 200,
  201: 201,
  202: 202,
  206: 206,
  
  // 客户端错误
  400: 400,
  401: 401,
  403: 403,
  404: 404,
  405: 405,
  409: 409,
  422: 422,
  429: 429,
  
  // 服务器错误
  500: 500,
  501: 501,
  502: 502,
  503: 503,
  504: 504,
  
  // 业务错误默认映射到400
  1001: 400, // USER_NOT_FOUND -> 400
  1002: 409, // USER_ALREADY_EXISTS -> 409
  1003: 403, // USER_DISABLED -> 403
  1004: 423, // USER_LOCKED -> 423
  
  2001: 401, // TOKEN_EXPIRED -> 401
  2002: 401, // TOKEN_INVALID -> 401
  2003: 401, // LOGIN_FAILED -> 401
  2004: 401, // PASSWORD_INCORRECT -> 401
  2005: 423, // ACCOUNT_LOCKED -> 423
  
  3001: 403, // PERMISSION_DENIED -> 403
  3002: 403, // ROLE_NOT_FOUND -> 403
  3003: 403, // INSUFFICIENT_PRIVILEGES -> 403
  
  4001: 400, // INSUFFICIENT_BALANCE -> 400
  4002: 400, // OPERATION_NOT_ALLOWED -> 400
  4003: 423, // RESOURCE_LOCKED -> 423
  4004: 409, // DUPLICATE_OPERATION -> 409
  4005: 410, // RESOURCE_EXPIRED -> 410
  
  5001: 503, // SYSTEM_MAINTENANCE -> 503
  5002: 429, // RATE_LIMIT_EXCEEDED -> 429
  5003: 502, // EXTERNAL_SERVICE_ERROR -> 502
  5004: 500, // DATABASE_ERROR -> 500
  5005: 500, // CACHE_ERROR -> 500
};

/**
 * 获取HTTP状态码
 */
export function getHttpStatusCode(businessCode: number): number {
  return CODE_TO_HTTP_STATUS[businessCode] || 500;
}

/**
 * 获取错误类型
 */
export function getErrorType(code: number): ErrorType {
  if (code >= 1000 && code < 2000) return ErrorType.BUSINESS_ERROR;
  if (code >= 2000 && code < 3000) return ErrorType.AUTHENTICATION_ERROR;
  if (code >= 3000 && code < 4000) return ErrorType.AUTHORIZATION_ERROR;
  if (code >= 4000 && code < 5000) return ErrorType.BUSINESS_ERROR;
  if (code >= 5000 && code < 6000) return ErrorType.SYSTEM_ERROR;
  if (code === 400 || code === 422) return ErrorType.VALIDATION_ERROR;
  if (code === 401) return ErrorType.AUTHENTICATION_ERROR;
  if (code === 403) return ErrorType.AUTHORIZATION_ERROR;
  if (code >= 500) return ErrorType.SYSTEM_ERROR;
  
  return ErrorType.SYSTEM_ERROR;
}

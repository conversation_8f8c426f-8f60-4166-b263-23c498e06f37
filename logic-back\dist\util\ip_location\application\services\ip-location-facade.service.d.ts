import { LoggerService } from '../../../../common/logger/logger.service';
import { IpLocationApplicationService } from './ip-location-application.service';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { HttpResponseResultService } from '../../../../web/http_response_result/http_response_result.service';
import { EnhancedHttpResponse, UnifiedResponse } from '../../../../web/http_response_result/http-response.interface';
import { RequestTraceService } from '../../../../web/http_response_result/request-trace.service';
export declare class IpLocationFacadeService {
    private readonly applicationService;
    private readonly domainService;
    private readonly logger;
    private readonly httpResponseService;
    private readonly requestTraceService?;
    constructor(applicationService: IpLocationApplicationService, domainService: IpLocationDomainService, logger: LoggerService, httpResponseService: HttpResponseResultService, requestTraceService?: RequestTraceService | undefined);
    getLocationByIP(ip: string, includeRisk?: boolean): Promise<EnhancedHttpResponse>;
    assessLoginRisk(userId: number, ip: string, userAgent?: string, sessionId?: string): Promise<EnhancedHttpResponse>;
    getUserLocationStats(userId: number, days?: number): Promise<EnhancedHttpResponse>;
    setTrustedLocation(userId: number, province: string, city: string, reason?: string): Promise<EnhancedHttpResponse>;
    updateUserCommonLocation(userId: number, ip: string): Promise<EnhancedHttpResponse>;
    testIpResolution(testIp?: string): Promise<EnhancedHttpResponse>;
    getLocationByIPUnified(ip: string, includeRisk?: boolean): Promise<UnifiedResponse>;
    checkUserPermission(userId: number): Promise<UnifiedResponse>;
    private generateRequestId;
    private getCacheStatus;
    private logPerformanceMetrics;
}

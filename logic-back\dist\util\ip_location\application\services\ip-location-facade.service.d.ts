import { LoggerService } from '../../../../common/logger/logger.service';
import { IpLocationApplicationService } from './ip-location-application.service';
import { IpLocationDomainService } from '../../domain/services/ip-location-domain.service';
import { HttpResponseResultService } from '../../../../web/http_response_result/http_response_result.service';
import { EnhancedHttpResponse } from '../../../../web/http_response_result/http-response.interface';
export declare class IpLocationFacadeService {
    private readonly applicationService;
    private readonly domainService;
    private readonly logger;
    private readonly httpResponseService;
    constructor(applicationService: IpLocationApplicationService, domainService: IpLocationDomainService, logger: LoggerService, httpResponseService: HttpResponseResultService);
    getLocationByIP(ip: string, includeRisk?: boolean): Promise<EnhancedHttpResponse>;
    assessLoginRisk(userId: number, ip: string, userAgent?: string, sessionId?: string): Promise<EnhancedHttpResponse>;
    getUserLocationStats(userId: number, days?: number): Promise<EnhancedHttpResponse>;
    setTrustedLocation(userId: number, province: string, city: string, reason?: string): Promise<EnhancedHttpResponse>;
    updateUserCommonLocation(userId: number, ip: string): Promise<EnhancedHttpResponse>;
    testIpResolution(testIp?: string): Promise<EnhancedHttpResponse>;
    private generateRequestId;
    private getCacheStatus;
    private logPerformanceMetrics;
}

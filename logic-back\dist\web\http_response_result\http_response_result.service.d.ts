import { HttpResponse, UnifiedResponse, EnhancedHttpResponse, EnhancedResponseOptions, ErrorType } from './http-response.interface';
import { LoggerService } from '../../common/logger/logger.service';
export declare class HttpResponseResultService {
    private readonly loggerService?;
    constructor(loggerService?: LoggerService | undefined);
    success<T = any>(data?: T, msg?: string, code?: number): HttpResponse<T>;
    error<T = any>(msg?: string, data?: T, code?: number): HttpResponse<T>;
    custom<T = any>(code: number, msg: string, data?: T): HttpResponse<T>;
    enhancedSuccess<T = any>(data?: T, msg?: string, code?: number, options?: EnhancedResponseOptions & {
        logContext?: {
            module: string;
            operation: string;
            userId?: string;
            requestData?: any;
        };
    }): EnhancedHttpResponse<T>;
    enhancedError<T = any>(msg?: string, data?: T, code?: number, options?: EnhancedResponseOptions & {
        logContext?: {
            module: string;
            operation: string;
            userId?: string;
            requestData?: any;
            error?: Error;
        };
    }): EnhancedHttpResponse<T>;
    enhancedCustom<T = any>(code: number, msg: string, data?: T, success?: boolean, options?: EnhancedResponseOptions): EnhancedHttpResponse<T>;
    unifiedSuccess<T = any>(data?: T, msg?: string, code?: number, options?: {
        requestId?: string;
        traceId?: string;
        path?: string;
        executionTime?: number;
        fromCache?: boolean;
    }): UnifiedResponse<T>;
    unifiedError<T = any>(msg?: string, data?: T, code?: number, options?: {
        requestId?: string;
        traceId?: string;
        path?: string;
        errorType?: ErrorType;
        errorDetails?: any;
        stack?: string;
        executionTime?: number;
    }): UnifiedResponse<T>;
    businessError<T = any>(businessCode: number, customMessage?: string, data?: T, options?: {
        requestId?: string;
        traceId?: string;
        path?: string;
        errorDetails?: any;
        executionTime?: number;
    }): UnifiedResponse<T>;
    private getBusinessErrorMessage;
}

import { HttpResponse, EnhancedHttpResponse, EnhancedResponseOptions } from './http-response.interface';
import { LoggerService } from '../../common/logger/logger.service';
export declare class HttpResponseResultService {
    private readonly loggerService?;
    constructor(loggerService?: LoggerService | undefined);
    success<T = any>(data?: T, msg?: string, code?: number): HttpResponse<T>;
    error<T = any>(msg?: string, data?: T, code?: number): HttpResponse<T>;
    custom<T = any>(code: number, msg: string, data?: T): HttpResponse<T>;
    enhancedSuccess<T = any>(data?: T, msg?: string, code?: number, options?: EnhancedResponseOptions & {
        logContext?: {
            module: string;
            operation: string;
            userId?: string;
            requestData?: any;
        };
    }): EnhancedHttpResponse<T>;
    enhancedError<T = any>(msg?: string, data?: T, code?: number, options?: EnhancedResponseOptions & {
        logContext?: {
            module: string;
            operation: string;
            userId?: string;
            requestData?: any;
            error?: Error;
        };
    }): EnhancedHttpResponse<T>;
    enhancedCustom<T = any>(code: number, msg: string, data?: T, success?: boolean, options?: EnhancedResponseOptions): EnhancedHttpResponse<T>;
}

# 响应日志记录完整指南

## 📋 概述

本指南详细说明了如何使用增强的响应日志记录系统，实现完整的请求响应追踪和问题定位。

## 🎯 功能特性

### ✅ **已实现功能**

1. **完整的请求响应日志记录**
   - 请求参数（Query、Body）
   - 响应内容
   - 执行时间
   - 状态码
   - 用户代理和IP

2. **敏感数据自动脱敏**
   - 密码、Token等敏感字段
   - 支付相关敏感信息
   - 可配置脱敏规则

3. **追踪ID支持**
   - 自动生成唯一追踪ID
   - 全链路日志关联
   - 便于问题定位

4. **智能日志分级**
   - 成功请求：INFO级别
   - 慢请求：WARN级别
   - 错误请求：ERROR级别

5. **业务上下文集成**
   - 模块和操作标识
   - 用户ID关联
   - 自定义元数据

## 🚀 使用方式

### 1. **自动HTTP日志记录**

#### 基础日志（现有功能保持不变）
```typescript
// 在 app.module.ts 中配置
@Module({
  // ...
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 基础HTTP日志中间件（保持向后兼容）
    consumer
      .apply(HttpLoggerMiddleware)
      .forRoutes('*');
  }
}
```

#### 增强日志（新功能）
```typescript
// 在 app.module.ts 中配置增强日志
@Module({
  // ...
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // 增强HTTP日志中间件（记录详细信息）
    consumer
      .apply(EnhancedHttpLoggerMiddleware)
      .forRoutes(
        { path: '/api/v1/ip-location/*', method: RequestMethod.ALL },
        { path: '/api/v1/payment/*', method: RequestMethod.ALL },
        { path: '/api/v1/auth/*', method: RequestMethod.ALL }
      );
  }
}
```

### 2. **业务服务中的日志记录**

#### 在DDD模块中使用
```typescript
@Injectable()
export class IpLocationFacadeService {
  constructor(
    private readonly httpResponseService: HttpResponseResultService
  ) {}

  async getLocationByIP(ip: string, includeRisk: boolean): Promise<EnhancedHttpResponse> {
    const startTime = Date.now();

    try {
      const result = await this.applicationService.queryIpLocation({ ip, includeRisk });

      // 使用增强响应服务，自动记录日志
      return this.httpResponseService.enhancedSuccess(
        result,
        '查询成功',
        200,
        {
          executionTime: Date.now() - startTime,
          meta: { requestId: this.generateRequestId() },
          logContext: {
            module: 'IpLocation',
            operation: 'queryLocation',
            requestData: { ip, includeRisk }
          }
        }
      );
    } catch (error) {
      return this.httpResponseService.enhancedError(
        error.message || '查询失败',
        null,
        500,
        {
          executionTime: Date.now() - startTime,
          logContext: {
            module: 'IpLocation',
            operation: 'queryLocation',
            requestData: { ip, includeRisk },
            error: error
          }
        }
      );
    }
  }
}
```

#### 在传统控制器中使用
```typescript
@Controller('api/v1/users')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly loggerService: LoggerService
  ) {}

  @Post('login')
  async login(@Body() loginDto: LoginDto, @Req() request: Request) {
    const startTime = Date.now();
    const traceId = (request as any).traceId;

    try {
      const result = await this.userService.login(loginDto);

      // 手动记录API响应日志
      this.loggerService.logApiResponse(
        'User',
        'login',
        {
          requestData: { username: loginDto.username }, // 密码已自动脱敏
          responseData: result,
          success: true,
          executionTime: Date.now() - startTime,
          userId: result.userId,
          traceId
        }
      );

      return result;
    } catch (error) {
      this.loggerService.logApiResponse(
        'User',
        'login',
        {
          requestData: { username: loginDto.username },
          responseData: null,
          success: false,
          executionTime: Date.now() - startTime,
          traceId,
          error
        }
      );

      throw error;
    }
  }
}
```

## 📊 日志格式示例

### 基础HTTP日志
```json
{
  "level": "info",
  "message": "HTTP Request",
  "context": "HTTP",
  "method": "GET",
  "url": "/api/v1/ip-location/query?ip=*******",
  "statusCode": 200,
  "responseTime": "45ms",
  "userAgent": "Mozilla/5.0...",
  "ip": "*************",
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

### 增强HTTP日志
```json
{
  "level": "info",
  "message": "[trace-1705312200000-abc123def] HTTP Request Success",
  "context": "HTTP_DETAILED",
  "method": "GET",
  "url": "/api/v1/ip-location/query?ip=*******",
  "statusCode": 200,
  "responseTime": "45ms",
  "userAgent": "Mozilla/5.0...",
  "ip": "*************",
  "traceId": "trace-1705312200000-abc123def",
  "requestQuery": { "ip": "*******", "includeRisk": "false" },
  "requestBody": null,
  "responseBody": {
    "code": 200,
    "msg": "查询成功",
    "data": { "ip": "*******", "country": "美国", "city": "山景城" },
    "success": true,
    "timestamp": "2025-01-15T10:30:00.000Z"
  },
  "isSuccess": true,
  "isError": false,
  "isSlowRequest": false,
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

### API响应日志
```json
{
  "level": "info",
  "message": "[trace-1705312200000-abc123def] API IpLocation.queryLocation Success",
  "context": "API_RESPONSE",
  "module": "IpLocation",
  "operation": "queryLocation",
  "success": true,
  "executionTime": "45ms",
  "traceId": "trace-1705312200000-abc123def",
  "requestData": "{\"ip\":\"*******\",\"includeRisk\":false}",
  "responseData": "{\"ip\":\"*******\",\"country\":\"美国\",\"city\":\"山景城\"}",
  "timestamp": "2025-01-15T10:30:00.000Z"
}
```

## 🔍 问题追踪流程

### 1. **前端报告问题**
```typescript
// 前端获取 requestId 或 traceId
const response = await fetch('/api/v1/ip-location/query?ip=*******');
const data = await response.json();

console.log('请求ID:', data.meta.requestId);
console.log('追踪ID:', response.headers.get('X-Trace-Id'));
```

### 2. **后端日志查询**
```bash
# 通过 traceId 查询完整链路日志
grep "trace-1705312200000-abc123def" /var/log/application/*.log

# 通过 requestId 查询业务日志
grep "ip-loc-1705312200000-abc123def" /var/log/application/*.log

# 查询特定模块的日志
grep "API IpLocation" /var/log/application/*.log
```

### 3. **日志分析**
```bash
# 完整的追踪链路示例
2025-01-15 10:30:00.123 [INFO] [trace-1705312200000-abc123def] Enhanced Incoming GET /api/v1/ip-location/query?ip=*******
2025-01-15 10:30:00.125 [INFO] [ip-loc-1705312200000-abc123def] 门面服务开始处理: IP=*******
2025-01-15 10:30:00.127 [INFO] [trace-1705312200000-abc123def] API IpLocation.queryLocation Success
2025-01-15 10:30:00.130 [INFO] [trace-1705312200000-abc123def] HTTP Request Success GET /api/v1/ip-location/query?ip=******* 200 45ms
```

## ⚙️ 配置选项

### 响应日志配置
```typescript
// 在环境变量或配置文件中设置
export const responseLoggingConfig: ResponseLoggingConfig = {
  enableDetailedLogging: true,
  logRequestBody: true,
  logResponseBody: true,
  slowRequestThreshold: 1000,
  sensitiveFields: ['password', 'token', 'qrCode'],
  excludePaths: ['/health', '/metrics'],
  detailedLogPaths: ['/api/v1/ip-location', '/api/v1/payment'],
  maxRequestBodySize: 10 * 1024,
  maxResponseBodySize: 50 * 1024
};
```

## 🎯 最佳实践

### 1. **日志级别选择**
- **生产环境**：使用基础HTTP日志 + 关键业务API的增强日志
- **测试环境**：使用增强HTTP日志记录所有接口
- **开发环境**：使用调试级别日志

### 2. **敏感数据处理**
- 自动脱敏密码、Token等敏感字段
- 支付相关数据特殊处理
- 可配置自定义敏感字段

### 3. **性能考虑**
- 大响应体自动截断
- 排除健康检查等高频接口
- 异步日志写入

### 4. **存储和查询**
- 建议使用ELK Stack进行日志聚合
- 按traceId建立索引便于查询
- 定期清理过期日志

---

**总结**: 通过这套完整的响应日志记录系统，你可以实现从前端到后端的全链路追踪，大大提升问题定位和系统监控的效率！🐱

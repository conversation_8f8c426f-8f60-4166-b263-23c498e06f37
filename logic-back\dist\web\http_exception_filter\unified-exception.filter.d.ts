import { ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
import { LoggerService } from '../../common/logger/logger.service';
export declare class UnifiedExceptionFilter implements ExceptionFilter {
    private readonly httpResponseResultService;
    private readonly loggerService;
    constructor(httpResponseResultService: HttpResponseResultService, loggerService: LoggerService);
    catch(exception: unknown, host: ArgumentsHost): void;
    private parseException;
    private buildErrorResponse;
    private logError;
    private getDefaultErrorMessage;
    private generateRequestId;
    private generateTraceId;
}
